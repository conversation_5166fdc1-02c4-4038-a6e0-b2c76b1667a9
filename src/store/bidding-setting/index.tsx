import { MutationTree, ActionTree } from 'vuex';

import { setting as settingService } from '@/shared/services';

import { SettingModel } from '../interfaces';

export const namespaced = true;

export const mutations: MutationTree = {
  SET_BIDDING_SETTING(state, payload: SettingModel) {
    state.biddingSettings = payload;
  },
};
export const actions: ActionTree = {
  async getBiddingSetting({ commit }, id?: number) {
    let res = {};
    try {
      res = await settingService.getTenderSettingHistory({ id });
    } catch (error) {
      console.error(error);
    } finally {
      commit('SET_BIDDING_SETTING', res || {});
    }
  },
  async createBiddingSetting({ commit }) {
    const res = await settingService.template('', 'tender_risk');
    commit('SET_BIDDING_SETTING', res || {});
  },
  async recoveryBiddingSetting({ commit }, id) {
    const res = await settingService.tenderRecovery(id);
    commit('SET_BIDDING_SETTING', res || {});
  },
};

export const state = {
  biddingSettings: {},
};
