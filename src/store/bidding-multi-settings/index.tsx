import { GetterTree, MutationTree, ActionTree } from 'vuex';

import { setting as settingService } from '@/shared/services';
import { isEmpty } from 'lodash';

export const namespaced = true;

export const mutations: MutationTree = {
  SET_DEFAULT_MODEL(state, payload) {
    state.defaultModel = payload;
  },
  SET_MODELS(state, payload) {
    state.models = payload;
  },
  SET_SELECTED_MODEL(state, payload) {
    state.selectedModel = payload;
  },
};
export const getters: GetterTree = {
  models(state) {
    return state.models;
  },
  defaultModel(state) {
    return state.defaultModel;
  },
  selectedModel(state) {
    return state.selectedModel;
  },
};
export const actions: ActionTree = {
  async getModelsList({ commit }) {
    const [res, defaultRes] = await Promise.all([settingService.tenderMultiList(), settingService.getTenderDefaultSetting()]);
    res?.data?.forEach((item) => {
      if (item.version === defaultRes.settingVersion) {
        item.default = true;
        commit('SET_DEFAULT_MODEL', item);
      } else {
        item.default = false;
      }
    });
    commit('SET_MODELS', res?.data ?? []);
    // 接口如果没返回默认模型，选第一个
    if (isEmpty(state.defaultModel)) {
      commit('SET_DEFAULT_MODEL', res?.data[0]);
    }
    commit('SET_SELECTED_MODEL', state.defaultModel);
  },
};

export const state = {
  models: [],
  defaultModel: {},
  selectedModel: {},
};
