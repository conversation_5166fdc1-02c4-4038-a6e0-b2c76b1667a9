import { isArray } from 'lodash';

import { IQRichTableColumn } from '@/components/global/q-rich-table';
import { numberToHuman } from '@/utils/number-formatter';

export const fieldNameMap = {
  equityAmount: '被冻结股权数额',
  penaltiesType: '处罚类型',
  executionSum: '被执行总金额',
  duration: '成立时间',
  penaltiesAmount: '处罚金额',
  taxArrearsAmount: '欠税余额',
  executionTarget: '执行标的',
  registrationAmount: '注册金额',
  capitalReduction: '资本降幅',
  changeThreshold: '变更阈值',
  operator: '运算符',
  cycle: '统计周期',
  percentAsDefendant: '被告占比',
  amountInvolved: '案件金额',
  isValid: '数据范围',
  businessAbnormalType: '列入原因',
  judgementRoleExclude: '案件身份',
  failure: '未履行总金额',
  guaranteedprincipal: '被保证债权本金',
  certification: '资质证书',
  nearExpirationType: '临近到期期限',
  assetLiabilityRatio: '资产负债率',
  punishReasonType: '处罚事由',
  associateObject: '关联对象',
  caseIdentity: '案件身份',
  companyInfo: '本公司名称',
  associateExclude: '关联排除',
  Certification: '证书类型',
  pledgeStatus: '质押状态',
  types: '关联类型',
  landMortgageAmount: '抵押金额',
  AmountOwed: '欠缴金额',
  recentTime: '期限限制',
  limitCount: '数量设置',
  amount: '被担保主债权数额',
  creditType: '处罚类型',
  simpleCancellationStep: '简易注销结果',
  billAcceptanceRiskStatus: '名单类型',
  employeeReductionRatio: '减少比例',
  paidInCapitalRatio: '实缴资本比例',
  conditionOperator: '匹配条件',
  registeredCapitalChangeRatio: '变更比例',
  listtypecode: '名单类型',
  capitalReductionRatio: '减资幅度',
  latestInsuredNumber: '参保人数',
};

export const conditionOptions = [
  { label: '并且', value: 1 },
  { label: '或者', value: 2 },
];

export const cycleMap = [
  { label: '近1年', value: 1 },
  { label: '近2年', value: 2 },
  { label: '近3年', value: 3 },
  { label: '不限', value: -1 },
];

export const cycleMap2 = [
  { label: '近7日', value: 1 },
  { label: '近1月', value: 2 },
  { label: '近3月', value: 3 },
];

export const radioOptionsMap = {
  approachingExpiry: [
    { label: '近1月', value: 1 },
    { label: '近3月', value: 3 },
    { label: '近半年', value: 6 },
  ],
  recentTime: cycleMap2,
  nearExpirationType: cycleMap2,
  isValid: [
    { label: '当前有效', value: '1' },
    { label: '不限', value: '-1' },
  ],
};

export const operatorMap = [
  // { label: '大于', value: 'gt' },
  { label: '大于等于', value: 'ge' },
  // { label: '等于', value: 'eq' },
  // { label: '小于', value: 'lt' },
  { label: '小于等于', value: 'le' },
];

const operatorMapLabel = {
  gt: '大于',
  ge: '大于等于',
  eq: '等于',
  lt: '小于',
  le: '小于等于',
};

export const punishOperatorMap = [
  { label: '包含', value: 'contain' },
  { label: '不包含', value: 'notContain' },
];

export const penaltiesTypeMap = [
  { label: '警告', value: '0901' },
  { label: '通报批评', value: '0902' },
  { label: '罚款', value: '0903' },
  { label: '没收违法所得', value: '0904' },
  { label: '没收非法财物', value: '0905' },
  { label: '暂扣许可证件', value: '0906' },
  { label: '降低资质等级', value: '0907' },
  { label: '吊销许可证/执照', value: '0908' },
  { label: '限制开展生产经营活动', value: '0909' },
  { label: '责令停产停业', value: '0910' },
  { label: '责令关闭', value: '0911' },
  { label: '限制申请行政许可', value: '0912' },
  { label: '限制从业', value: '0913' },
  { label: '行政拘留', value: '0914' },
  { label: '移送司法机关', value: '0915' },
  { label: '不予处罚', value: '0916' },
  { label: '其他行政处罚', value: '0999' },
];

// 环保处罚-处罚类型
export const envPenaltiesTypeMap = [
  { label: '吊销许可证件（含执照）', value: '0908' },
  { label: '责令关闭', value: '0911' },
  { label: '移送司法机关', value: '0915' },
  { label: '责令停产停业', value: '0910' },
  { label: '行政拘留', value: '0914' },
  { label: '限制开展生产经营活动', value: '0909' },
  { label: '限制从业', value: '0913' },
  { label: '罚款', value: '0903' },
  { label: '没收违法所得', value: '0904' },
  { label: '警告', value: '0901' },
  { label: '通报批评', value: '0902' },
  { label: '没收非法财物', value: '0905' },
  { label: '暂扣许可证件（含执照）', value: '0906' },
  { label: '降低资质等级', value: '0907' },
  // { label: '限制申请行政许可', value: '0912' },
  { label: '其他处罚', value: '0999' },
  { label: '不予处罚', value: '0916' },
];

export const punishReasonTypeMap = [
  { label: '围串标', value: '201' },
  { label: '分包/转包/挂靠', value: '202' },
  { label: '虚假材料', value: '203' },
  { label: '商业贿赂', value: '301' },
  { label: '其他', value: '0' },
];

export const businessAbnormalTypeMap = [
  { label: '公示信息隐瞒真实情况/弄虚作假', value: '0803' },
  { label: '登记的住所/经营场所无法联系企业', value: '0801' },
  { label: '未在规定期限公示年度报告', value: '0805' },
  { label: '未按规定公示企业信息', value: '0802' },
  { label: '未在登记所从事经营活动', value: '0804' },
  { label: '商事主体名称不适宜', value: '0806' },
  { label: '其他原因', value: '0807' },
];

export const pledgeStatusMap = [
  { label: '已解除质押', value: 0 },
  { label: '未达预警线', value: 1 },
  { label: '已达预警线未达平仓线', value: 2 },
  { label: '已达平仓线', value: 3 },
  { label: '其他', value: 4 },
];

export const billAcceptanceRiskStatusList = [
  { label: '持续逾期名单', value: 2 },
  { label: '承兑人逾期名单', value: 1 },
  { label: '延迟披露名单', value: 4 },
  { label: '信用信息未披露名单', value: 3 },
];

export const billAcceptanceRiskStatusMap = {
  1: '承兑人逾期名单',
  2: '持续逾期名单',
  3: '信用信息未披露名单',
  4: '延迟披露名单',
};

export const creditTypeMap = [
  {
    value: 'A112',
    label: '暂停相关业务',
  },
  {
    value: 'A111',
    label: '暂停或者限制交易权限',
  },
  {
    value: 'A110',
    label: '严重警告',
  },
  {
    value: 'A113',
    label: '责令改正',
  },
  {
    value: 'A101',
    label: '公开谴责',
  },
  {
    value: 'A104',
    label: '监管警示',
  },
  {
    value: 'A102',
    label: '监管关注',
  },
  {
    value: 'A103',
    label: '监管函',
  },
  {
    value: 'A105',
    label: '诫勉谈话',
  },
  {
    value: 'A117',
    label: '警告',
  },
  {
    value: 'A106',
    label: '警示函',
  },
  {
    value: 'A107',
    label: '内部批评',
  },
  {
    value: 'A109',
    label: '书面警示',
  },
  {
    value: 'A116',
    label: '通报批评',
  },
  {
    value: 'A108',
    label: '认定不适当人选',
  },
  {
    value: 'A114',
    label: '责令致歉',
  },
  {
    value: 'A115',
    label: '自律管理',
  },
  {
    value: 'A199',
    label: '其他处理措施',
  },
];

export const armyProcurementIllegalOptions = [
  {
    value: '75',
    label: '军队采购失信名单',
  },
  {
    value: '76',
    label: '军队采购暂停供应商资格',
  },
  {
    value: '104',
    label: '军队采购预警名单',
  },
  {
    value: '107',
    label: '军队采购知情告知书',
  },
];

export const optionLabelsMap = {
  'EnvironmentalPenalties.penaltiesType': envPenaltiesTypeMap,
  punishReasonType: punishReasonTypeMap,
  penaltiesType: penaltiesTypeMap,
  pledgeStatus: pledgeStatusMap,
  creditType: creditTypeMap,
  businessAbnormalType: businessAbnormalTypeMap,
  billAcceptanceRiskStatus: billAcceptanceRiskStatusList,
  listtypecode: armyProcurementIllegalOptions,
};

export const typeMap = {
  keyItems: '关键项',
  generalItems: '一般项',
};

export const dimensionUnitMap = {
  duration: '个月',
  percentAsDefendant: '%',
  assetLiabilityRatio: '%',
  paidInCapitalRatio: '%',
  registeredCapitalChangeRatio: '%',
  registrationAmount: '万元',
  equityAmount: '',
  landMortgageAmount: '万元',
  guaranteeAmount: '万元',
  chattelMortgageMainAmount: '万元',
  amount: '万元',
  limitCount: '个',
  pledgeStatus: '',
  judgementRole: '',
  businessAbnormalType: '',
  punishReasonType: '',
  penaltiesType: '',
  creditType: '',
  billAcceptanceRiskStatus: '',
  employeeReductionRatio: '%',
  listtypecode: '',
  capitalReductionRatio: '%',
  latestInsuredNumber: '人',
};

export const settingConditionsFilter = (item) => {
  if (!Array.isArray(item?.strategyModel?.detailsParams)) {
    return '-';
  }

  let result = '';

  item.strategyModel.detailsParams.forEach((c) => {
    if (c?.fieldOperator) {
      if (result === '-') {
        result = '';
      }

      if (['penaltiesType', 'punishReasonType'].includes(c.field)) {
        const ops = optionLabelsMap[`${item.key}.${c.field}`] ?? optionLabelsMap[c.field];
        const punishOperator = punishOperatorMap.find((e) => e.value === c.fieldOperator)?.label;
        let types = '';
        if (isArray(c.fieldVal)) {
          c.fieldVal.forEach((v, index: any) => {
            const penaltiesType = ops.find((e) => e.value === v)?.label;
            types += `${penaltiesType}${index !== c.fieldVal.length - 1 ? '、' : ''}`;
          });
          result += types ? `${`${fieldNameMap[c.field]}: ${punishOperator}${types}`}; \n` : '';
        }
      } else {
        const operator = operatorMapLabel[c.fieldOperator];
        result += `${`${fieldNameMap[c.field]}: ${operator}${numberToHuman(c.fieldVal)}`}${dimensionUnitMap[c.field] ?? '元'}\n`;
      }
    } else if (['topics'].includes(c.field) && c.fieldVal) {
      const text = c.fieldVal
        .filter((ops: any) => ops.status)
        .reduce((str, op, idx, arr) => {
          str += `${op.keyName}${idx !== arr.length - 1 ? ',' : ''}`;
          return str;
        }, '');
      result += `主题类型: ${text}\n`;
    } else if (['associateObject', 'caseIdentity', 'associateExclude', 'simpleCancellationStep'].includes(c.field)) {
      const qualification = c.fieldVal
        ?.filter((v) => v.status === 1)
        ?.map((v) => v.keyName)
        ?.join('、');
      result += qualification ? `${fieldNameMap[c.field]}: ${qualification}\n` : '';
    } else if (['pledgeStatus', 'businessAbnormalType', 'creditType', 'billAcceptanceRiskStatus'].includes(c.field) && c.fieldVal?.length) {
      const labels = optionLabelsMap[c.field]
        .filter((v) => c.fieldVal.includes(v.value))
        .map((v) => v.label)
        .join('、');
      result += `${fieldNameMap[c.field]}: ${labels}\n`;
    } else if (['nearExpirationType', 'recentTime', 'approachingExpiry'].includes(c.field)) {
      const label = radioOptionsMap[c.field].find((v) => v.value === c.fieldVal)?.label ?? '-';
      result += `${fieldNameMap[c.field] ?? c.fieldName}: ${label} \n`;
    } else if (c.field === 'businessLicense') {
      result += c.status === 1 ? `${fieldNameMap[item.key]}: ${c.fieldName}` : '';
    } else if (['certification', 'taxpayerList'].includes(c.field)) {
      // 营业证书和资质证书合并显示
      const qualification = c.fieldVal
        .filter((v) => v.status === 1)
        .map((v) => v.keyName)
        .join('、');
      const prefix = result ? `、` : `${fieldNameMap[item.key]}: `;
      result += qualification ? `${prefix}${qualification}` : '';
      result = `${result.split('\n').join('')}`;
      result = `${result ? `${result}\n` : ''}`;
    } else if (['conditionOperator'].includes(c.field)) {
      const val = conditionOptions.find((v) => v.value === c.fieldVal)?.label ?? '-';
      result += `${fieldNameMap[c.field]}: ${val}`;
    } else if (['companyInfo'].includes(c.field)) {
      result += `${fieldNameMap[c.field]}: ${c.fieldVal?.companyName || '-'}\n`;
    }
  });
  return result ?? '-';
};

export const TABLE_COLUMNS: IQRichTableColumn[] = [
  {
    title: '排序',
    width: 56,
    align: 'center',
    customCell: () => {
      return {
        style: { color: '#d8d8d8' },
      };
    },
    scopedSlots: { customRender: 'draggle' },
  },
  {
    title: '指标项',
    align: 'left',
    scopedSlots: { customRender: 'riskName' },
  },
  {
    title: '指标类型',
    width: 120,
    customRender: (item) => {
      return typeMap[item?.type] ?? '-';
    },
  },
  {
    title: '风险等级',
    width: 120,
    scopedSlots: { customRender: 'riskLevel' },
  },
  {
    title: '统计周期',
    width: 120,
    customRender: (item) => {
      if (item.strategyModel?.cycle) {
        return cycleMap.find((v) => v.value === item.strategyModel?.cycle)?.label ?? '-';
      }
      return '-';
    },
  },
  // {
  //   title: '数据范围',
  //   width: 120,
  //   customRender: (text, item) => {
  //     if (item.strategyModel?.detailsParams && isArray(item.strategyModel?.detailsParams)) {
  //       const key = item.strategyModel?.detailsParams.find((v) => v.field === 'isValid')?.fieldVal;
  //       return isValidMap.find((v) => v.value === key)?.label ?? '-';
  //     }
  //     return '-';
  //   },
  // },
  {
    title: '条件筛选',
    width: 220,
    scopedSlots: { customRender: 'investigationCondition' },
  },
  {
    title: '启用指标',
    width: 79,
    scopedSlots: { customRender: 'enabled' },
  },
  {
    title: '操作',
    width: 60,
    scopedSlots: { customRender: 'action' },
  },
];

export const OUTER_BLACK_COLUMNS: IQRichTableColumn[] = [
  {
    title: '排序',
    width: 56,
    align: 'center',
    customCell: () => {
      return {
        style: { color: '#d8d8d8' },
      };
    },
    scopedSlots: { customRender: 'draggle' },
  },
  {
    title: '黑名单类型',
    align: 'left',
    scopedSlots: { customRender: 'riskName' },
  },
  {
    title: '风险等级',
    width: 80,
    scopedSlots: { customRender: 'riskLevel' },
  },
  {
    title: '启用指标',
    width: 79,
    scopedSlots: { customRender: 'enabled' },
  },
  {
    title: '操作',
    width: 60,
    scopedSlots: { customRender: 'action' },
  },
];

export const validatorMap = {
  CompanyOrMainMembersLitigationInfo: [
    {
      key: 'companyInfo',
      validator: (data) => {
        const detailsParams = data?.strategyModel?.detailsParams || [];
        const companyInfo = detailsParams.find((v) => v.field === 'companyInfo')?.fieldVal || {};
        return data.status === 1 ? !!companyInfo?.companyId : true;
      },
      message: '请输入本公司名称',
    },
  ],
};
