import { mount } from '@vue/test-utils';
import { ref } from 'vue';
import { message } from 'ant-design-vue';

import { useMultiSettingStore } from '@/hooks/use-multi-setting-store';
import { setting } from '@/shared/services';

import DiligenceItem from '..';

const promisifyFn = () => vi.fn().mockResolvedValue({});
const mockRouter = {
  options: {
    routes: [],
  },
  push: promisifyFn(),
  replace: promisifyFn(),
  back: promisifyFn(),
  go: promisifyFn(),
  forward: promisifyFn(),
  resolve: promisifyFn(),
  catch: vi.fn(),
};
vi.mock('vue-router/composables', function () {
  return {
    __esModule: true,
    useRouter() {
      return mockRouter;
    },
    useRoute() {
      return {};
    },
  };
});
vi.mock('@/hooks/use-multi-setting-store');
vi.mock('@/shared/services');
vi.mock('ant-design-vue');

const mockUseMultiSettingStore = {
  canCreate: ref(true),
  list: ref([{}, {}]),
};

const mockSetting = {
  edit: vi.fn(),
  remove: vi.fn(),
};

vi.mocked<any>(useMultiSettingStore).mockReturnValue(mockUseMultiSettingStore);
(setting as any).edit = mockSetting.edit;
(setting as any).remove = mockSetting.remove;

describe('DiligenceItem', () => {
  test('should render with no props', () => {
    const wrapper = mount(DiligenceItem);
    expect(wrapper.html()).toContain('第三方风险排查标准模型');
  });

  test('should render correctly', () => {
    const wrapper = mount(DiligenceItem, {
      propsData: {
        value: {
          id: '1',
          name: 'Test Model',
          description: 'Test Description',
          creator: { name: 'Test Creator' },
          createDate: new Date().toISOString(),
        },
      },
    });
    expect(wrapper.html()).toContain('Test Model');
  });

  test('should handle go update', async () => {
    const wrapper = mount(DiligenceItem, {
      propsData: {
        value: {
          id: '1',
        },
      },
    });
    await wrapper.vm.handleGoUpdate();
    expect(mockRouter.push).toHaveBeenCalledWith({
      name: 'investigation-setting-detail',
      params: { type: 'edit' },
      query: { id: '1' },
    });
  });

  test('should handle copy model', async () => {
    const wrapper = mount(DiligenceItem, {
      propsData: {
        value: {
          id: '1',
        },
      },
    });
    await wrapper.vm.handleCopyModel({ stopPropagation: vi.fn() });
    expect(mockRouter.push).toHaveBeenCalledWith({
      name: 'investigation-setting-detail',
      params: { type: 'copy' },
      query: { id: '1' },
    });
  });

  test('should show warning if canCreate is false', async () => {
    mockUseMultiSettingStore.canCreate.value = false;
    const wrapper = mount(DiligenceItem, {
      propsData: {
        value: {
          id: '1',
        },
      },
    });
    await wrapper.vm.handleCopyModel({ stopPropagation: vi.fn() });
    expect(message.warning).toHaveBeenCalledWith('您可使用的模型数量已达上限！');
  });

  test('should handle change enabled', async () => {
    const wrapper = mount(DiligenceItem, {
      propsData: {
        value: {
          id: '1',
        },
      },
    });
    await wrapper.vm.handleChangeEnabled();
    expect(mockSetting.edit).toHaveBeenCalledWith({ id: '1' });
  });

  test('should handle delete', async () => {
    const wrapper = mount(DiligenceItem, {
      propsData: {
        value: {
          id: '1',
        },
      },
    });
    await wrapper.vm.handleDelete();
    expect(mockSetting.remove).toHaveBeenCalledWith('1');
    expect(message.success).toHaveBeenCalledWith('删除成功');
  });
});
