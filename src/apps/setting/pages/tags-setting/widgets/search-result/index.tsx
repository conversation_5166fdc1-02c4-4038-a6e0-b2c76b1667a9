import { <PERSON><PERSON>, <PERSON>confirm, Space, Spin } from 'ant-design-vue';
import { defineComponent, PropType } from 'vue';

import { IQRichTablePagination } from '@/components/global/q-rich-table';
import PartnerTag from '@/shared/components/partner-tag';
import QIcon from '@/components/global/q-icon';
import QRichTableEmpty from '@/components/global/q-rich-table/components/empty';

import styles from './search-result.module.less';

const GroupsSearchResult = defineComponent({
  name: 'GroupsSearchResult',
  props: {
    dataSource: {
      type: Array,
      required: true,
      default: () => [],
    },
    pagination: {
      type: [Object, Boolean] as PropType<Partial<IQRichTablePagination> | boolean>,
      required: false,
      default: false,
    },

    loading: {
      type: Boolean,
      default: true,
    },
  },
  emits: ['create', 'delete', 'edit'],
  setup(props, { emit }) {
    const handleDelete = (id) => {
      emit('delete', id);
    };

    const handleCreate = () => {
      emit('create');
    };

    return {
      handleDelete,
      handleCreate,
    };
  },
  render() {
    const { dataSource, loading } = this;
    if (loading) {
      return (
        <Spin>
          <div class="flex items-center justify-center" style={{ height: 'calc(100vh - 210px)' }}></div>
        </Spin>
      );
    }
    if (!dataSource?.length) {
      return (
        <QRichTableEmpty size={'100px'} minHeight={'calc(100vh - 155px)'}>
          <span class={styles.empty}>
            <div>暂无标签</div>
            <Space>
              <Button onClick={this.handleCreate} type="primary">
                新建标签
              </Button>
            </Space>
          </span>
        </QRichTableEmpty>
      );
    }
    return (
      <div>
        <div class="flex flex-wrap" style={{ gap: '5px' }}>
          {dataSource.map((item: any) => {
            return (
              <Popconfirm placement="bottom" title="您确认删除该标签吗？" onConfirm={() => this.handleDelete(item.labelId)}>
                <PartnerTag key={item.labelId} color={item.color}>
                  <span>{item.name}</span>

                  <QIcon style={{ fontSize: '8px', marginLeft: '5px', color: '#bbb' }} type="icon-tanchuangguanbi" />
                </PartnerTag>
              </Popconfirm>
            );
          })}
        </div>
        <Button class={styles.btnCreate} icon="plus" onClick={this.handleCreate}>
          新建标签
        </Button>
      </div>
    );
  },
});

export default GroupsSearchResult;
