import { Button, message, Popconfirm } from 'ant-design-vue';
import { cloneDeep } from 'lodash';
import { computed, defineComponent, onMounted, ref, unref, nextTick } from 'vue';

import QCard from '@/components/global/q-card';
import { setting as settingService } from '@/shared/services';
import { hasPermission } from '@/shared/composables/use-permission';
import { createTrackEvent, useTrack } from '@/config/tracking-events';
import SaveBeforeLeave from '@/apps/setting/components/save-before-leave';
import UpgradeReminder from '@/apps/setting/components/upgrade-reminder';

import SettingBlock from './widgets/setting-block';
import { openTenderUpdateDrawer } from './widgets/tender-update-drawer';
import styles from './tender-setting.module.less';
import { useBiddingSettingStore } from '@/hooks/use-bidding-setting-store';
import { dateFormat } from '@/utils/format';
import HeroicLayout from '@/shared/layouts/heroic';
import { useRequest } from '@/shared/composables/use-request';
import { useRoute, useRouter } from 'vue-router/composables';
import TemplateForm from '../../components/template-form';

// TODO 埋点补充
const BiddingSettingDetailPage = defineComponent({
  name: 'BiddingSettingDetailPage',
  props: {
    isMulti: {
      type: Boolean,
      default: true,
    },
  },
  setup(props) {
    const track = useTrack();
    const route = useRoute();
    const router = useRouter();
    const formRef = ref();

    const type = computed(() => route.params.type);
    const isCreate = computed(() => type.value === 'create');

    const { result, getSetting, createSetting, recoverySetting } = useBiddingSettingStore();

    const fetchData = async () => {
      const id = route.query.id;
      switch (type.value) {
        case 'edit':
        case 'copy':
          await getSetting(id ? Number(id) : undefined);
          break;
        case 'create':
          await createSetting();
          break;
        default:
          await getSetting();
      }
      return result.value;
    };

    const { execute, isLoading } = useRequest(fetchData);

    const isUpgrade = ref(false);

    const permission = ref(hasPermission([2086]));

    const saveSetting = async (data) => {
      if (!permission.value) return;
      let saveFn;
      let msg;
      switch (type.value) {
        case 'create':
        case 'copy':
          saveFn = settingService.tenderMultiAdd;
          msg = '创建成功';
          data.systemSettingsId = data.systemSettingsId || data.id;
          break;
        default:
          saveFn = settingService.saveTenderSetting;
          msg = '保存成功';
      }
      await saveFn(data);
      if (isUpgrade.value) {
        isUpgrade.value = false;
        message.success('已为您更新到最新版');
      } else {
        message.success(msg);
      }
    };

    const isEdit = ref(props.isMulti);

    const handleCancel = async () => {
      isEdit.value = false;
      await nextTick();
      if (props.isMulti) {
        router.push({
          name: 'bidding-investigation-setting',
        });
      } else {
        await execute();
      }
    };

    const handleSubmit = async () => {
      try {
        const data = await formRef.value?.getData();
        await saveSetting(data);
        handleCancel();
      } catch (err) {
        console.error(err);
      }
    };

    onMounted(() => {
      execute();
    });

    const handleRecovery = async () => {
      track(createTrackEvent(7725, '招标排查设置', '恢复系统默认'));
      await recoverySetting(result.value.id);
      message.success('恢复成功');
    };

    const handleOpenUpgradeModal = async () => {
      track(createTrackEvent(7725, '招标排查设置', '立即查看'));
      const data: any = await openTenderUpdateDrawer({ ...result.value, permission: [2086] });
      if (data) {
        isEdit.value = true;
        isUpgrade.value = true;
        const changeData = cloneDeep(data);
        unref(result).canUpgrade = false;
        Object.assign(result.value, changeData);
        track(createTrackEvent(7725, '招标排查设置', '更新到最新版本'));
      } else {
        isUpgrade.value = false;
      }
    };

    return {
      permission,
      execute,
      isLoading,
      handleSubmit,
      handleOpenUpgradeModal,
      isEdit,
      result,
      isCreate,
      handleRecovery,
      handleCancel,
      formRef,
    };
  },
  render() {
    const title = { edit: '编辑模型', create: '新建模型', copy: '复制模型' }[this.$route.params.type];

    return (
      <HeroicLayout loading={this.isLoading} showPageEnd>
        <QCard
          title={title || '招标排查设置'}
          headerSticky
          titleBorder={false}
          headerStyle={{ fontSize: '18px', padding: '3px 16px' }}
          bodyStyle={{ padding: '15px' }}
        >
          <div slot="extra" class="flex gap-10px items-center">
            {this.isCreate ? null : (
              <Popconfirm title="确定要恢复到系统默认设置吗？" onConfirm={this.handleRecovery} v-permission={[2082]}>
                <Button type="link">恢复系统默认</Button>
              </Popconfirm>
            )}
            <div v-show={this.result?.creator?.name || this.result?.createDate} class="h-32px leading-32px text-14px flex gap-10px">
              <span>（更新人：{this.result?.creator?.name ?? '-'}</span>
              <span>更新时间：{dateFormat(this.result.createDate, { pattern: 'YYYY-MM-DD HH:mm:ss', defaultVal: '-' })}）</span>
            </div>

            <SaveBeforeLeave
              v-model={this.isEdit}
              permission={[2086]}
              trackInfo={{ pageCode: 7725, pageName: '招标排查设置' }}
              saveFn={this.handleSubmit}
              onAfterCancel={this.handleCancel}
            />
          </div>

          <UpgradeReminder canUpgrade={this.result?.canUpgrade} text="招标排查" onClick={this.handleOpenUpgradeModal} />

          {this.isMulti ? <TemplateForm ref="formRef" isEdit={this.isEdit} result={this.result} permission={[2086]} /> : null}

          {this.isMulti ? (
            <div class={styles.desc}>
              <div class={styles.title}>排查指标设置：</div>
              <div class={styles.content}>
                支持自定义维度、统计周期、筛选条件等，自定义维度包括:关联关系排査、涉采购不良行为、共同投标分析、内部黑名单、潜在利益冲突
              </div>
            </div>
          ) : null}

          <SettingBlock v-model={this.result.content} disabled={!this.isEdit} />
        </QCard>
      </HeroicLayout>
    );
  },
});

export default BiddingSettingDetailPage;
