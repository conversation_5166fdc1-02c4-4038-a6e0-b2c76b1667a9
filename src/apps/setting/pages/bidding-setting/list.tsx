import { computed, defineComponent, onMounted, unref } from 'vue';
import Draggable from 'vuedraggable';
import { useRouter } from 'vue-router/composables';
import { Button, message } from 'ant-design-vue';

import HeroicLayout from '@/shared/layouts/heroic';
import QCard from '@/components/global/q-card';
import { setting } from '@/shared/services';
import { createTrackEvent, useTrack } from '@/config/tracking-events';
import { useI18n } from '@/shared/composables/use-i18n';

import DiligenceItem from '../../components/diligence-item';
import { useUserStore } from '@/shared/composables/use-user-store';
import { useRequest } from '@/shared/composables/use-request';

const BiddingSettingListPage = defineComponent({
  name: 'BiddingSettingListPage',
  setup() {
    const track = useTrack();
    const router = useRouter();
    const { execute, data, isLoading } = useRequest(setting.tenderMultiList);
    const list = computed(() => data.value?.data || []);
    const canCreate = computed(() => {
      const { usage } = useUserStore();
      return unref(list).length < (unref(usage)?.diligenceModelQuantity?.limitation || 0);
    });
    const handleCreate = async () => {
      track(createTrackEvent(7721, '招标排查设置', '新建模型'));
      if (!canCreate.value) {
        message.warning('您可使用的模型数量已达上限！');
        return;
      }
      router.push({
        name: 'bidding-setting-detail',
        params: {
          type: 'create',
        },
      });
    };
    const handleSort = async () => {
      await setting.tenderMultiSort(list.value?.map((item) => item.id));
      track(createTrackEvent(7721, '招标排查设置', '拖拽排序'));
    };

    /**
     * 删除模型
     * @param id
     */
    const handleDelete = async (id) => {
      track(createTrackEvent(7721, '招标排查设置', '删除模型'));
      await setting.tenderMultiDelete(id);
      message.success('删除成功');
      execute();
    };
    /**
     * 复制模型
     * @param id
     * @returns
     */
    const handleCopy = async (id) => {
      track(createTrackEvent(7721, '招标排查设置', '复制模型'));
      if (!canCreate.value) {
        message.warning('您可使用的模型数量已达上限！');
        return;
      }
      router.push({
        name: 'bidding-setting-detail',
        params: { type: 'copy' },
        query: { id },
      });
    };
    /**
     * 编辑模型
     * @param id
     */
    const handleEdit = async (id) => {
      track(createTrackEvent(7721, '招标排查设置', '编辑模型'));
      router.push({
        name: 'bidding-setting-detail',
        params: { type: 'edit' },
        query: { id },
      });
    };
    onMounted(() => {
      // search();
      execute();
    });
    const { tc } = useI18n();
    const title = computed(() => tc('Tender Settings'));
    return {
      handleCreate,
      handleSort,
      list,
      isLoading,
      execute,
      canCreate,
      title,
      handleCopy,
      handleEdit,
      handleDelete,
    };
  },
  render() {
    return (
      <HeroicLayout loading={this.isLoading}>
        <QCard
          class={'diligence-setting'}
          title={this.title}
          headerSticky
          titleBorder={false}
          headerStyle={{ fontSize: '18px', padding: '3px 16px' }}
          bodyStyle={{ padding: '0px 0px 10px' }}
        >
          <div slot="extra">
            <Button icon="plus-circle" type="primary" onClick={this.handleCreate}>
              新建模型
            </Button>
          </div>
          <Draggable list={this.list} handle=".drag-handle" animation="300" onChange={this.handleSort}>
            {this.list.map((item) => {
              return (
                <DiligenceItem
                  value={item}
                  defaultTitle="第三方招标排查标准模型"
                  deletable={this.list.length > 1}
                  on={{
                    delete: this.handleDelete,
                    copy: this.handleCopy,
                    edit: this.handleEdit,
                  }}
                />
              );
            })}
          </Draggable>
        </QCard>
      </HeroicLayout>
    );
  },
});

export default BiddingSettingListPage;
