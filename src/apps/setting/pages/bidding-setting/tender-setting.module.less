.pageTitle {
  font-size: 18px;
  font-weight: 500;
  line-height: 24px;
  color: #333;
}

.upgrade-description {
  padding: 16px;
  border: 1px solid rgba(18, 139, 237, 0.2);
  background: #f2f8fe;
  border-radius: 4px;
  margin-bottom: 16px;

  .title {
    color: #333;
    font-weight: bold;
    margin-bottom: 5px;
  }

  .content {
    color: #666;
    white-space: break-spaces;
  }
}

.footer {
  text-align: right;
  padding: 16px;
  position: sticky;
  bottom: 0;
  background-color: #fff;

  &::before {
    content: '';
    position: absolute;
    left: 16px;
    right: 16px;
    top: 0;
    height: 1px;
    background-color: #eee;
  }

  button + button {
    margin-left: 16px;
  }
}

.desc {
  border-radius: 4px;
  border: 1px solid rgba(18, 139, 237, 0.2);
  padding: 9px 16px;
  display: flex;
  flex-direction: column;
  background: #f2f8fe;
  gap: 5px;
  margin-bottom: 10px;

  .title {
    color: #333;
    font-size: 14px;
    line-height: 22px;
    font-weight: bold;
  }

  .content {
    color: #666;
    font-size: 14px;
    line-height: 22px;
  }
}
