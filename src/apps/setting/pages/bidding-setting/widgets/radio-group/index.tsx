import { Radio } from 'ant-design-vue';
import { get } from 'lodash';
import { PropType, computed, defineComponent } from 'vue';

const RadioGroup = defineComponent({
  name: 'RadioGroup',
  props: {
    settingParam: {
      type: Object as PropType<any>,
      required: true,
      default: () => ({}) as any,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  setup(props) {
    const initValue = computed(() => get(props.settingParam, props.settingParam.valueKeyPath || props.settingParam.key, []));
    return {
      initValue,
    };
  },
  render() {
    const { disabled, settingParam, initValue } = this;
    return (
      <Radio.Group
        disabled={disabled}
        value={initValue}
        options={settingParam.options}
        size="small"
        onChange={(e) => {
          this.$emit('radioChange', e.target.value);
        }}
      />
    );
  },
});

export default RadioGroup;
