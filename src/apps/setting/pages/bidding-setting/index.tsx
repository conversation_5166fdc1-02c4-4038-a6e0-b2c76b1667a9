import { useUserStore } from '@/shared/composables/use-user-store';
import { defineComponent } from 'vue';
import BiddingSettingListPage from './list';
import BiddingSettingDetailPage from './detail';

const BiddingSettingPage = defineComponent({
  name: 'BiddingSettingPage',
  setup() {
    const { isBiddingMultiple } = useUserStore();
    return {
      isBiddingMultiple,
    };
  },
  render() {
    if (this.isBiddingMultiple) return <BiddingSettingListPage />;
    return <BiddingSettingDetailPage isMulti={false} />;
  },
});

export default BiddingSettingPage;
