import { radioOptionsMap } from '@/apps/setting/pages/investigation-setting/widgets/risk-settings/risk-settings.config';
import { IQRichTableColumn } from '@/components/global/q-rich-table';
import { numberToHuman } from '@/utils/number-formatter';

export const operatorMap = [
  // { label: '大于', value: 'gt' },
  { label: '大于等于', value: 'ge' },
  // { label: '等于', value: 'eq' },
  // { label: '小于', value: 'lt' },
  { label: '小于等于', value: 'le' },
];

// 万元单位列
export const WanYuanList = ['guaranteeAmount', 'landMortgageAmount', 'chattelMortgageMainAmount'];

export const MathValueKeyList = [
  'executionSum',
  'taxArrearsAmount',
  'landMortgageAmount',
  'chattelMortgageMainAmount',
  'guaranteeAmount',
  'caseAmount',
  'penaltiesAmount',
  'limitCount',
  'registeredCapitalChangeRatio',
  'capitalReductionRatio',
];

export const fieldNameMap = {
  MonitorPersonExecution: '被执行金额',
  MonitorTaxArrearsNotice: '欠税金额',
  MonitorChattelMortgage: '动产抵押金额',
  MonitorLandMortgage: '土地抵押金额',
  MonitorEquityPledge: '股权出质金额',
  MonitorGuaranteeInfo: '担保金额',
  MonitorImportantDispute: '案件金额',
  MonitorCertificationExpired: '证书类型',
  nearExpirationType: '临近到期期限',
  registeredCapitalChangeRatio: '变更比例',
};

const renderUnit = (field) => {
  if (['limitCount'].includes(field)) {
    return '个';
  }
  if (['registeredCapitalChangeRatio', 'capitalReductionRatio'].includes(field)) {
    return '%';
  }
  return WanYuanList.includes(field) ? '万元' : '元';
};

export const conditionFilterCustomRender = (item: Record<string, any>): string => {
  let result = '';
  if (Array.isArray(item.params)) {
    const fieldVals = item.params;
    fieldVals.forEach((fieldObj) => {
      if (MathValueKeyList.includes(fieldObj.field)) {
        const fieldOperatorName = operatorMap.find((opration) => opration.value === fieldObj.fieldOperator)?.label;
        result += `${fieldObj.fieldName || fieldNameMap[item.key]}: ${fieldOperatorName} ${numberToHuman(
          fieldObj.fieldVal
        )}${renderUnit(fieldObj.field)}\n`;
      } else if (
        [
          'penaltiesType',
          'blackListType',
          'businessStatus',
          'simpleCancellationStep',
          'reasonType',
          'foreignInvestmentChangeType',
          'billAcceptanceRiskStatus',
        ].includes(fieldObj.field)
      ) {
        const qualification = fieldObj.fieldVal
          .filter((v) => v.status === 1)
          .map((v) => v.keyName)
          .join('、');
        result += `${fieldObj.fieldName}: ${qualification}\n`;
      } else if (fieldObj.field === 'nearExpirationType') {
        const label = radioOptionsMap[fieldObj.field].find((v) => v.value === fieldObj.fieldVal)?.label || '-';
        result += `${fieldObj.fieldName || fieldNameMap[fieldObj.field]}: ${label}\n`;
      } else if (fieldObj.field === 'businessLicense') {
        result += fieldObj.status === 1 ? `${fieldNameMap[item.key]}: ${fieldObj.fieldName}` : '';
      } else if (fieldObj.field === 'certification') {
        const qualification = fieldObj.fieldVal
          .filter((v) => v.status === 1)
          .map((v) => v.keyName)
          .join('、');
        // 营业证书和资质证书合并显示
        const prefix = result ? `、` : `${fieldNameMap[item.key]}: `;
        result += qualification ? `${prefix}${qualification}` : '';
        result += result ? `\n` : '';
      }
    });
  }
  return result;
};

export const TABLE_COLUMNS: IQRichTableColumn[] = [
  {
    title: '排序',
    width: 56,
    align: 'center',
    customCell: () => {
      return {
        style: { color: '#d8d8d8' },
      };
    },
    scopedSlots: { customRender: 'draggle' },
  },
  {
    title: '指标项',
    align: 'left',
    scopedSlots: { customRender: 'riskName' },
  },
  {
    title: '风险等级',
    width: 120,
    scopedSlots: { customRender: 'riskLevel' },
  },
  {
    title: '条件筛选',
    width: 300,
    scopedSlots: { customRender: 'monitorCondition' },
  },
  {
    title: '启用指标',
    width: 120,
    scopedSlots: { customRender: 'enabled' },
  },
  {
    title: '操作',
    width: 60,
    scopedSlots: { customRender: 'action' },
  },
];
