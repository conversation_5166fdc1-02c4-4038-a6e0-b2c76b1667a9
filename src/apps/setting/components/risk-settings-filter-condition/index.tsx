import { Checkbox, Col, InputN<PERSON>ber, Radio, Row, Select, Button, Icon } from 'ant-design-vue';
import { intersection, isArray, isEmpty, union, uniq, xor } from 'lodash';
import { computed, defineComponent, onMounted, PropType, reactive, ref, unref } from 'vue';

import TagSelect from '@/apps/setting/pages/subscription-setting/widgets/tag-select';
import QSwitch from '@/components/global/q-switch';
import { convertNegativeNewsOptionGroups, useLabelGroupHooks } from '@/shared/composables/use-label-group';
import QGlossaryInfo from '@/components/global/q-glossary-info';
import QIcon from '@/components/global/q-icon';
import { conditionOptions, fieldNameMap } from '@/apps/setting/pages/investigation-setting/widgets/risk-settings/risk-settings.config';
import MultiSelect from '@/components/multi-select';

import { thirdDepartmentOptions, thirdGroupOptions, thirdRelationOptionsMap } from './config';
import styles from './risk-settings-filter-condition.module.less';
import { operatorMap } from '../../pages/cooperative-monitor-setting/modules/risk-dynamics/widgets/risk-settings/risk-settings.config';
import { SUSPECTDEPTH } from '../../pages/bidding-setting/config';
import CerfiticationTreeWrapper from './widgets/cerfitication-tree-wrapper';

// 匹配条件
const renderConditionOperator = (value, onChange) => {
  return (
    <div style="margin-top: 15px;">
      <div class={styles.subHeader}>匹配条件</div>
      <div>
        <Radio.Group class={styles.conditionRadioGroup} value={value} options={conditionOptions} onChange={onChange}></Radio.Group>
        {/* <p style={{ padding: '2px 0' }}>
          <QIcon type="icon-icon_zhushi" style={{ color: '#D8D8D8', marginRight: '5px' }} />
          <span style={{ color: '#FFAA00' }}>注意：所设范围内的企业数量不得大于5000家，否则将无法输出排查结果！</span>
        </p> */}
      </div>
    </div>
  );
};

/**
 * 第三方、黑名单设置范围单独处理
 */
export const GroupAndTagsFilterCondition = defineComponent({
  name: 'GroupAndTagsFilterCondition',
  props: {
    item: {
      type: Object as PropType<any>,
      required: true,
    },
    labels: {
      type: Array as PropType<any[]>,
      required: true,
    },
    groups: {
      type: Array as PropType<any[]>,
      required: true,
    },
  },
  emits: ['change'],
  setup(props, { emit }) {
    const actualGroups = computed(() => {
      if (props.item.version === 'v2') {
        return props.groups.map((v) => ({ ...v, disabled: v.groupId === -1 }));
      }
      return props.groups;
    });

    const isAllEmptyValue = computed(() => {
      if (!Array.isArray(props.item.fieldVal)) {
        return true;
      }
      return props.item.fieldVal.every(({ value }) => isEmpty(value));
    });

    const handleChange = (values, itemKey, isOther = false) => {
      emit('change', values, [itemKey], isOther);
    };

    /** 显示内容 */
    const isOptionsVisible = ref(!isAllEmptyValue.value);
    const handleSwitchVisible = (isVisible: boolean) => {
      isOptionsVisible.value = isVisible;
      if (!isVisible) {
        // 切换为关闭时，需要清除当前已选内容
        emit(
          'change',
          [],
          props.item.fieldVal.map(({ key }) => key)
        );
      }
    };
    return {
      actualGroups,
      isOptionsVisible,
      handleSwitchVisible,
      handleChange,
    };
  },
  render() {
    const isMultiple = this.item.fieldVal.length > 1;
    const isV2 = this.item.version === 'v2';
    return (
      <div class={styles.section}>
        <div class={styles.header}>
          <QSwitch class="third-black-switch" size="medium" checked={this.isOptionsVisible} onChange={this.handleSwitchVisible} />
        </div>
        <div class={[styles.body, styles.fixed]} v-show={this.isOptionsVisible}>
          <div class={styles.subHeader} v-show={isMultiple}>
            范围
          </div>
          {this.item.fieldVal.map((subItem) => {
            const isGroup = subItem.key === 'Groups'; // 首个为分组类型
            const isConditionOperator = subItem.key === 'conditionOperator';

            if (isConditionOperator) {
              return renderConditionOperator(subItem.value, (e) => {
                const val = e.target.value;
                this.handleChange({ value: val }, subItem.key, true);
              });
            }

            return (
              <div class={styles.groupLabel}>
                {isMultiple || isV2 ? <div style="height: 22px;line-height: 22px;margin-top: 5px;">{subItem.keyName}</div> : null}

                {isGroup ? (
                  // 第三方、黑名单标签
                  <MultiSelect
                    placeholder="请选择分组"
                    style="flex: 1"
                    value={subItem.value}
                    options={this.actualGroups}
                    class={styles.groupSelect}
                    labelKey="name"
                    valueKey="groupId"
                    inline
                    onChange={(values: string[]) => {
                      this.handleChange(values, subItem.key);
                    }}
                  />
                ) : (
                  // 第三方、黑名单分组
                  <TagSelect
                    style="flex: 1;"
                    placeholder="请选择标签"
                    value={subItem.value}
                    valueKey="labelId"
                    options={this.labels}
                    onChange={(values: string[]) => {
                      this.handleChange(values, subItem.key);
                    }}
                  />
                )}
              </div>
            );
          })}
          <div v-show={isV2} class="text-#bbb flex items-center gap-col-[4px] mt-2">
            <QIcon type="icon-a-shuomingxian" />
            <span class="text-#666">注意：选择的所有分组下内部黑名单企业数量不得大于 5000 家，否则将无法输出排查结果！</span>
          </div>
        </div>
      </div>
    );
  },
});

export const CustomerPartnerCondition = defineComponent({
  name: 'CustomerPartnerCondition',
  props: {
    item: {
      type: Object as PropType<any>,
      required: true,
    },
    labels: {
      type: Array as PropType<any[]>,
      required: true,
    },
    groups: {
      type: Array as PropType<any[]>,
      required: true,
    },
    departments: {
      type: Array as PropType<any[]>,
      required: true,
    },
  },
  emits: ['change'],
  setup(props, { emit }) {
    const handleChange = (values, itemKey?, isOtherData = false) => {
      emit('change', values, [itemKey], isOtherData);
    };

    const fieldsChecked = reactive({
      Groups: false,
      Labels: false,
      Departments: false,
    });
    const initFilterVal = () => {
      props.item.fieldVal.forEach((subItem) => {
        if (subItem.key === 'Departments') {
          fieldsChecked.Departments = !!subItem.nameList?.length;
        } else {
          fieldsChecked[subItem.key] = !!subItem.value?.length;
        }
      });
    };

    onMounted(() => {
      initFilterVal();
    });

    return {
      handleChange,
      fieldsChecked,
    };
  },
  render() {
    return (
      <div class={styles.section}>
        <div class={[styles.body, styles.fixed]}>
          <div class={styles.subHeader}>范围</div>
          {this.item.fieldVal.map((subItem) => {
            const isGroup = subItem.key === 'Groups'; // 首个为分组类型
            const isLabel = subItem.key === 'Labels';
            const isDepartment = subItem.key === 'Departments';
            const isUnlimited = subItem.type === 2;
            const isConditionOperator = subItem.key === 'conditionOperator';

            const radioOptions = thirdRelationOptionsMap[subItem.key];

            if (isConditionOperator) {
              return renderConditionOperator(subItem.value, (e) => {
                const val = e.target.value;
                this.handleChange({ value: val }, subItem.key, true);
              });
            }

            return (
              <div class={styles.customerPartnerCondition}>
                <div class={styles.conditionItem}>
                  <div class={styles.title}>{subItem.keyName}</div>
                  <Radio.Group
                    class="flex-1"
                    options={radioOptions}
                    defaultValue={subItem.type}
                    onChange={(e) => {
                      const val = e.target.value;
                      this.handleChange({ type: val, value: [], nameList: [] }, subItem.key, true);
                    }}
                  />
                  <Button
                    v-show={isUnlimited}
                    class={styles.filterBtn}
                    size="small"
                    onClick={() => {
                      this.fieldsChecked[subItem.key] = !this.fieldsChecked[subItem.key];
                      this.handleChange({ nameList: [], value: [] }, subItem.key, true);
                    }}
                  >
                    <QIcon v-show={!this.fieldsChecked[subItem.key]} type="icon-tianjia" />
                    <Icon v-show={this.fieldsChecked[subItem.key]} type="minus" />
                    <span>{this.fieldsChecked[subItem.key] ? '取消筛选' : '添加筛选'}</span>
                  </Button>
                </div>

                {isGroup ? (
                  <MultiSelect
                    v-show={this.fieldsChecked.Groups && isUnlimited}
                    placeholder="请选择分组"
                    style="width: 100%"
                    value={subItem.value}
                    options={this.groups}
                    class={styles.groupSelect}
                    labelKey="name"
                    valueKey="groupId"
                    inline
                    onChange={(values: string[]) => {
                      this.handleChange(values, subItem.key);
                    }}
                  />
                ) : null}
                {isLabel ? (
                  <TagSelect
                    v-show={this.fieldsChecked.Labels && isUnlimited}
                    style="width: 100%"
                    placeholder="请选择标签"
                    value={subItem.value}
                    valueKey="labelId"
                    options={this.labels}
                    onChange={(values: string[]) => {
                      this.handleChange(values, subItem.key);
                    }}
                  />
                ) : null}
                {isDepartment ? (
                  <MultiSelect
                    v-show={this.fieldsChecked.Departments && isUnlimited}
                    placeholder="请选择部门"
                    style="width: 100%"
                    value={subItem.nameList}
                    options={this.departments}
                    class={styles.groupSelect}
                    labelKey="name"
                    valueKey="name"
                    inline
                    onChange={(values: string[]) => {
                      const keys = this.departments
                        .filter((item) => values.includes(item.name))
                        .map((item) => item.departmentId)
                        .filter(Boolean);
                      this.handleChange(
                        {
                          value: keys,
                          nameList: values,
                        },
                        subItem.key,
                        true
                      );
                    }}
                  />
                ) : null}
              </div>
            );
          })}
        </div>
      </div>
    );
  },
});

export const CustomerPartnerConditionV2 = defineComponent({
  name: 'CustomerPartnerConditionV2',
  props: {
    item: {
      type: Object as PropType<any>,
      required: true,
    },
    groups: {
      type: Array as PropType<any[]>,
      required: true,
    },
  },
  emits: ['change', 'fieldChange'],
  setup(props, { emit }) {
    const groupsWithoutDefault = computed(() => props.groups.map((v) => ({ ...v, disabled: v.groupId === -1 })));

    const handleChange = (values, itemKey?, isOtherData = false) => {
      emit('change', values, [itemKey], isOtherData);
    };

    const groupField = computed(() => props.item.fieldVal.find((v) => v.key === 'Groups'));
    const departmentField = computed(() => props.item.fieldVal.find((v) => v.key === 'Departments'));

    const selectedGroups = computed(() => {
      return unref(groupField)?.value || [];
    });

    const departmentRadioValue = ref<number | undefined>();
    const groupRadioValue = ref<number | undefined>();

    onMounted(() => {
      departmentRadioValue.value = unref(departmentField).status ? unref(departmentField).type : undefined;
      groupRadioValue.value = unref(groupField).status ? unref(groupField).type : undefined;
    });

    return {
      groupsWithoutDefault,
      groupField,
      departmentField,
      departmentRadioValue,
      groupRadioValue,
      selectedGroups,
      handleChange,
    };
  },
  render() {
    return (
      <div class={styles.section}>
        <div class={[styles.body, styles.fixed]}>
          <div class={styles.subHeader}>排查范围</div>
          <div class={styles.customerPartnerCondition}>
            <div>
              <Radio.Group
                options={thirdDepartmentOptions}
                v-model={this.departmentRadioValue}
                onChange={(e) => {
                  const val = e.target.value;
                  if (!val) return;
                  this.groupRadioValue = undefined;
                  const fieldVal = [
                    { ...this.departmentField, status: 1, type: val },
                    { ...this.groupField, nameList: [], value: [], status: 0 },
                  ];
                  this.$emit('fieldChange', fieldVal);
                }}
              />
              <Radio.Group
                options={thirdGroupOptions}
                v-model={this.groupRadioValue}
                onChange={(e) => {
                  const val = e.target.value;
                  if (!val) return;
                  this.departmentRadioValue = undefined;
                  const fieldVal = [
                    { ...this.groupField, nameList: [], value: [], status: 1, type: val },
                    { ...this.departmentField, status: 0 },
                  ];
                  this.$emit('fieldChange', fieldVal);
                }}
              />
            </div>

            {this.groupRadioValue === 2 ? (
              <div class="mt-2">
                <MultiSelect
                  placeholder="请选择分组"
                  style="width: 100%"
                  value={this.selectedGroups}
                  options={this.groupsWithoutDefault}
                  class={styles.groupSelect}
                  labelKey="name"
                  valueKey="groupId"
                  inline
                  onChange={(groupIds: number[]) => {
                    this.handleChange(groupIds, 'Groups');
                  }}
                />
                <div class="text-#bbb flex items-center gap-col-[4px] mt-2">
                  <QIcon type="icon-a-shuomingxian" />
                  <span class="text-#666">注意：选择的所有分组下第三方企业数量不得大于 5000 家，否则将无法输出排查结果！</span>
                </div>
              </div>
            ) : null}
          </div>
        </div>
      </div>
    );
  },
});

const RiskSettingsFilterCondition = defineComponent({
  name: 'RiskSettingsFilterCondition',
  props: {
    item: {
      type: Object,
      required: true,
    },
    dimension: {
      type: String,
      required: true,
    },
  },
  emits: ['change'],
  setup(props, { emit }) {
    const {
      labels,
      groups,
      departments,
      getLabelOptions,
      getGroupOptions,
      getDepartments,
      convertToOptions,
      convertToSelectedValues,
      selectedValues,
      options,
      handleChangeData,
    } = useLabelGroupHooks(props, { emit });

    // 是否是第三方企业或者黑名单有关的维度
    const isThirdOrBlackList = computed(() => {
      return [
        'CustomerPartnerInvestigation', // 与第三方列表企业存在投资任职关联
        'BlacklistPartnerInvestigation', // 与黑名单列表企业存在投资任职关联
      ].includes(props.dimension);
    });

    // 过滤 status 为 1
    const subOptions = computed(() => {
      if (!isArray(props.item.fieldVal)) {
        return [];
      }
      return props.item.fieldVal.map(({ key, status, child }) => {
        return {
          key,
          status,
          child,
          options: status === 1 && !isEmpty(child) ? convertToOptions(child) : [],
        };
      });
    });

    const showShareholdingRatio = computed(() => {
      const value = selectedValues.value();
      const hasValue =
        intersection(value, [
          'ForeignInvestment',
          'HisForeignInvestment',
          'Shareholder',
          'HisInvestorsRelationship',
          'HisShareholdingRelationship',
          'ShareholdingRelationship',
          'InvestorsRelationship',
        ]).length > 0;

      const ShareholdingRatioItem = props.item.fieldVal?.find((item) => item.key === 'ShareholdingRatio');
      if (ShareholdingRatioItem) {
        ShareholdingRatioItem.status = hasValue ? 1 : 0;
      }
      return isThirdOrBlackList.value && hasValue;
    });

    onMounted(async () => {
      if (props.item.field === 'dataRange') {
        const typeMap = {
          CustomerPartnerInvestigation: 1,
          CustomerSuspectedRelation: 1,
          StaffWorkingOutsideForeignInvestment: 2,
          SuspectedInterestConflict: 2,
          BlacklistPartnerInvestigation: 3,
          BlacklistSuspectedRelation: 3,
          HitInnerBlackList: 3,
        };
        const type = typeMap[props.dimension];
        if (type !== 2) {
          await getLabelOptions(type);
        }
        if (type === 1) {
          await getDepartments();
        }
        await getGroupOptions(type);
      }
    });
    return {
      options,
      subOptions,
      selectedValues,
      labels,
      groups,
      departments,
      isThirdOrBlackList,
      showShareholdingRatio,
      convertToOptions,
      convertToSelectedValues,
      handleChangeData,
    };
  },
  render() {
    const { isThirdOrBlackList } = this;
    const relationItem = isArray(this.item.fieldVal) ? this.item.fieldVal.find((item) => item.key === 'ShareholdingRatio') : {};

    // 近3年负面新闻
    // 3年外年负面新闻
    const renderNegativeNewsRecent = () => {
      const optionGroups = convertNegativeNewsOptionGroups(this?.item?.fieldVal || []);
      const flattenSelectedValues = optionGroups.reduce((list, cur) => list.concat(cur.children), []); // flatMap children
      // 当前选中的值
      const selectedValues = flattenSelectedValues.filter(({ status }) => status === 1).map(({ key }) => key);
      /**
       * 处理复选框变更回调
       */
      const handleCheckboxChange = (currentSelectedValues: string[], hasChecked: boolean) => {
        let result;
        if (hasChecked) {
          // 全选
          result = union(selectedValues, currentSelectedValues);
        } else {
          // 取消全选
          result = xor(selectedValues, currentSelectedValues);
        }
        this.handleChangeData(result);
      };

      return (
        <div>
          {optionGroups.map(({ name, children }) => {
            const selectedChildrenOptions = children.filter(({ key }) => selectedValues.includes(key)); // 当前选中的子项
            const hasGroupAllChecked = selectedChildrenOptions.length === children.length; // 是否全选中
            const indeterminate = selectedChildrenOptions.length > 0 && !hasGroupAllChecked; // 全选/半选状态
            const allChildrenValues = children.map(({ key }) => key); // 全部子项的值

            return (
              <div key={name}>
                <div class={styles.header} data-count={children.length}>
                  <Checkbox
                    indeterminate={indeterminate}
                    checked={hasGroupAllChecked}
                    onChange={(ev) => handleCheckboxChange(allChildrenValues, ev.target.checked)}
                  >
                    <strong>{name}</strong>
                  </Checkbox>
                </div>
                <div class={[styles.body, styles.fixed]}>
                  <Row type="flex">
                    {children.map(({ key, keyName }) => {
                      const hasChecked = selectedValues.includes(key);
                      return (
                        <Col key={key} span={8}>
                          <Checkbox value={key} checked={hasChecked} onChange={(ev) => handleCheckboxChange([key], ev.target.checked)}>
                            {keyName}
                          </Checkbox>
                        </Col>
                      );
                    })}
                  </Row>
                </div>
              </div>
            );
          })}
        </div>
      );
    };

    const renderBusinessLicense = () => {
      return (
        <div>
          <div class={styles.header}>资质类型</div>
          <div class={styles.header}>
            <Checkbox
              checked={this.item.status === 1}
              onChange={(e) => {
                this.$emit('change', {
                  ...this.item,
                  status: Number(e.target.checked),
                });
              }}
            >
              <strong>{this.item.fieldName}</strong>
            </Checkbox>
          </div>
        </div>
      );
    };
    // 纳税人资质
    const renderTaxPayerCertification = () => {
      const checked = this.item.fieldVal.every((item) => item.status === 1);
      return (
        <div class={styles.section}>
          <div class={styles.header} style={{ fontWeight: 'bold' }}>
            <Checkbox
              checked={checked}
              indeterminate={uniq(this.item.fieldVal.map((child) => child.status))?.length !== 1}
              onChange={(e) => {
                const val = Number(e.target.checked);
                this.$emit('change', {
                  ...this.item,
                  fieldVal: this.item.fieldVal.map((child) => ({ ...child, status: val })),
                });
              }}
            >
              {this.item.fieldName}
            </Checkbox>
          </div>
          <div class={[styles.taxPayerBody]}>
            {this.item.fieldVal.map((fieldObj) => {
              return (
                <Checkbox
                  checked={fieldObj.status === 1}
                  onChange={(e) => {
                    const val = Number(e.target.checked);
                    this.$emit('change', {
                      ...this.item,
                      fieldVal: this.item.fieldVal.map((child) => ({
                        ...child,
                        status: child.key === fieldObj.key ? val : child.status,
                      })),
                    });
                  }}
                >
                  {fieldObj.keyName}
                </Checkbox>
              );
            })}
          </div>
        </div>
      );
    };

    const renderCertification = (selectAllLabel?: string) => {
      return (
        <CerfiticationTreeWrapper
          options={this.item.fieldVal}
          onUpdate={(values) => {
            this.$emit('change', {
              ...this.item,
              fieldVal: values,
            });
          }}
        >
          <span slot="title" style={{ fontWeight: '700' }}>
            {selectAllLabel || this.item.fieldName}
          </span>
        </CerfiticationTreeWrapper>
      );
    };

    // 过期时间
    const renderValidPeriod = () => {
      return (
        <div>
          <div class={styles.header}>{this.item.fieldName || fieldNameMap[this.item.field]}</div>
          <div style="padding-left: 10px;">
            <Radio.Group
              value={this.item?.fieldVal}
              onChange={(e) => {
                this.$emit('change', {
                  ...this.item,
                  fieldVal: e.target.value,
                });
              }}
            >
              <Radio value={1}>近7天</Radio>
              <Radio value={2}>近1月</Radio>
              <Radio value={3}>近3月</Radio>
            </Radio.Group>
          </div>
        </div>
      );
    };

    // 数量有关
    const renderCount = () => {
      const operatorLabel = operatorMap.find((opration) => opration.value === this.item.fieldOperator)?.label;
      return (
        <div>
          <div class={styles.header}>{this.item.fieldName}</div>
          <div style="margin: 0 0 0 10px;">
            <span>{operatorLabel}</span>
            <InputNumber
              style={{ margin: '0 5px' }}
              class={styles.input}
              v-model={this.item.fieldVal}
              min={0}
              precision={0}
              formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              parser={(value) => value.replace(/\$\s?|(,*)/g, '')}
            />
            <span>个</span>
          </div>
        </div>
      );
    };
    // 处罚类型
    const renderPenaltiesType = () => {
      const options = this.options('keyName').map((v) => ({
        ...v,
        disabled: v.value === '不限' ? false : this.selectedValues('keyName').includes('不限'),
      }));
      return (
        <div>
          <div class={styles.header} v-show={this.item.field === 'blackListType'}>
            {this.item.fieldName}
          </div>
          <MultiSelect
            style={{ width: '550px', margin: this.item.field === 'blackListType' ? '0 10px' : 0 }}
            value={this.selectedValues('keyName')}
            class={styles.groupSelect}
            allowClear={true}
            inline
            placeholder="请选择"
            options={options}
            onChange={(values: string[]) => {
              const isAll = values.includes('不限');
              this.item.fieldVal.forEach((op) => {
                if ((isAll && op.keyName === '不限') || (!isAll && values.includes(op.keyName))) {
                  op.status = 1;
                } else {
                  op.status = 0;
                }
              });
            }}
          ></MultiSelect>
        </div>
      );
    };
    const renderExcludedTypes = () => {
      const PartnerInvestigationOptions = [
        { label: '企业已吊销/注销', value: 'Revocation' },
        {
          label: '投资机构对外投资',
          value: 'InvestmentAgency',
          info: '排除节点主体因为是投资机构对外投资一个或多个企业形成的关联关系。',
        },
      ];
      const SuspectedRelationOptions = [
        {
          label: '疑似代记账(邮箱、电话)',
          value: 'SuspectedBookKeeping',
        },
      ];
      const excludedTypesMap = {
        BlacklistPartnerInvestigation: PartnerInvestigationOptions,
        BlacklistSuspectedRelation: SuspectedRelationOptions,
        CustomerPartnerInvestigation: PartnerInvestigationOptions,
        CustomerSuspectedRelation: SuspectedRelationOptions,
      };
      const options = excludedTypesMap[this.dimension];
      if (!options) {
        return null;
      }
      return (
        <div class={[styles.section, styles.depthContainer]}>
          <div class={styles.label}>节点排除</div>
          <Checkbox.Group
            defaultValue={this.item.fieldVal}
            onChange={(values: string[]) => {
              this.$emit('change', { ...this.item, fieldVal: values });
            }}
          >
            {options.map(({ value, label, info }) => {
              return (
                <Checkbox value={value}>
                  {label}
                  {info && <QGlossaryInfo tooltip={info} />}
                </Checkbox>
              );
            })}
          </Checkbox.Group>
        </div>
      );
    };

    // 关联层级
    const renderDepth = () => {
      return (
        <div class={[styles.section, styles.depthContainer]}>
          <div class={styles.label}>{this.item.fieldName || '关联层级'}</div>
          <Select
            options={SUSPECTDEPTH.slice(0, 3)}
            value={this.item.fieldVal}
            onChange={(e) => this.$emit('change', { ...this.item, fieldVal: e })}
          />
        </div>
      );
    };
    const renderOther = () => {
      return (
        <div class={styles.section} style={isThirdOrBlackList ? { background: '#F9F9F9' } : {}}>
          <div v-show={isThirdOrBlackList} class={styles.header}>
            关联类型
          </div>
          <div class={[styles.body, styles.fixed, isThirdOrBlackList ? styles.paddingcontent : '']}>
            <Checkbox.Group
              value={this.selectedValues()}
              // options={this.options()}
              onChange={(values: string[]) => {
                this.handleChangeData(values);
              }}
            >
              {this.options().map(({ label, value }) => {
                return (
                  <Checkbox value={value} style={{ margin: 0 }}>
                    {label}
                  </Checkbox>
                );
              })}
            </Checkbox.Group>
            {this.showShareholdingRatio && relationItem ? (
              <div
                style={{
                  color: '#666',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '10px',
                  marginTop: '6px',
                }}
              >
                <span>{relationItem.keyName}</span>
                <div class={styles.inputNumber}>
                  <span
                    style={{
                      position: 'absolute',
                      left: '7px',
                      top: '49%',
                      transform: 'translateY(-50%)',
                      zIndex: '10',
                      color: '#999',
                      fontSize: '12px',
                    }}
                  >
                    {'≥'}
                  </span>
                  <span
                    style={{
                      position: 'absolute',
                      right: '22px',
                      top: '50%',
                      transform: 'translateY(-50%)',
                      zIndex: '10',
                      color: '#999',
                      fontSize: '12px',
                    }}
                  >
                    {'%'}
                  </span>
                  <InputNumber
                    min={0.01}
                    max={100}
                    style={{ width: '100px' }}
                    precision={2}
                    value={relationItem.value}
                    // 解决值为0的时候输入框突然消失的问题
                    onChange={(e) => {
                      let res = e;
                      if (e < 0.01) {
                        res = 0.01;
                      }
                      if (res > 100) {
                        res = 100.0;
                      }
                      this.$set(relationItem, 'value', res);
                    }}
                  ></InputNumber>
                </div>
              </div>
            ) : null}
          </div>
        </div>
      );
    };
    // 一级维度/父
    const renderContent = () => {
      if (this.item.field === 'dataRange' && isArray(this.item.fieldVal)) {
        const isRiskPartnerInvestigation = ['CustomerPartnerInvestigation', 'CustomerSuspectedRelation'].includes(this.dimension);
        const isModelV1 = this.item.fieldVal.some((item) => item.key === 'Departments');
        const isModelV2 = this.item.version === 'v2';
        if (isRiskPartnerInvestigation && isModelV2) {
          return (
            <CustomerPartnerConditionV2
              item={this.item}
              groups={this.groups}
              onChange={this.handleChangeData}
              onFieldChange={(val) => this.$emit('change', { ...this.item, fieldVal: val })}
            />
          );
        }
        return isRiskPartnerInvestigation && isModelV1 ? (
          <CustomerPartnerCondition
            item={this.item}
            groups={this.groups}
            labels={this.labels}
            departments={this.departments}
            onChange={this.handleChangeData}
          />
        ) : (
          <GroupAndTagsFilterCondition item={this.item} groups={this.groups} labels={this.labels} onChange={this.handleChangeData} />
        );
      }
      let content = null;
      switch (this.item.field) {
        // 负面新闻
        case 'topics':
          content = renderNegativeNewsRecent();
          break;
        case 'penaltiesType':
        case 'blackListType':
        case 'businessStatus': // 经营状态
        case 'simpleCancellationStep': // 简易注销
        case 'reasonType': // 经营异常
        case 'foreignInvestmentChangeType': // 经营异常
        case 'billAcceptanceRiskStatus': // 票据承兑（监控设置）
          content = renderPenaltiesType();
          break;
        case 'certification':
          content = renderCertification(this.dimension === 'Certification' ? '资质证书' : this.item.fieldName); // Checkbox
          break;
        case 'nearExpirationType':
          content = renderValidPeriod();
          break;
        case 'limitCount':
          content = renderCount();
          break;
        // 营业执照
        case 'businessLicense':
          content = renderBusinessLicense();
          break;
        // 纳税人资质
        case 'taxpayerList':
          content = renderTaxPayerCertification();
          break;
        case 'depth':
          content = renderDepth();
          break;
        case 'excludedTypes':
          content = renderExcludedTypes();
          break;
        default:
          content = renderOther();
      }
      return content;
    };
    // 子维度
    const renderSubChildren = () => {
      return this.subOptions.map(({ key, options, child }) => {
        const selectedValues = this.convertToSelectedValues(child);
        return (
          <div class={styles.section} key={key} v-show={!isEmpty(options)}>
            <div class={styles.header}>排查对象</div>
            <div class={styles.body}>
              <Checkbox.Group
                value={selectedValues}
                options={options}
                onChange={(values: string[]) => {
                  this.handleChangeData(values, [key]);
                }}
              />
            </div>
          </div>
        );
      });
    };
    return (
      <div class={styles.container}>
        {/* 父级 */}
        {renderContent()}
        {/* 子级 */}
        {renderSubChildren()}
      </div>
    );
  },
});

export default RiskSettingsFilterCondition;
