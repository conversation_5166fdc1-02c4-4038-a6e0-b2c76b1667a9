import { FormModel, Input } from 'ant-design-vue';
import { defineComponent, PropType, reactive, ref, unref, watch } from 'vue';
import styles from './template-form.module.less';
import { hasPermission } from '@/shared/composables/use-permission';

const TemplateForm = defineComponent({
  name: 'TemplateForm',
  props: {
    permission: {
      type: Array as PropType<number[]>,
      default: () => [],
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    result: {
      type: Object,
      default: () => ({}),
    },
  },
  expose: ['getData'],
  setup(props) {
    const formRef = ref();

    const dataSource = reactive({
      name: '',
      description: '',
    });

    watch(
      () => props.result,
      (val) => {
        dataSource.name = val.name;
        dataSource.description = val.description;
      }
    );

    const getData = async () => {
      await formRef.value?.validate();
      return {
        ...props.result,
        ...unref(dataSource),
      };
    };

    return {
      formRef,
      dataSource,
      getData,
    };
  },
  render() {
    const rules = {
      name: [
        {
          required: true,
          message: '请输入模板名称',
        },
      ],
    };
    return (
      <FormModel
        ref="formRef"
        colon={false}
        rules={rules}
        props={{ model: this.dataSource }}
        label-col={{ span: 2 }}
        wrapper-col={{ span: 22 }}
        labelAlign="left"
        class={styles.formWrapper}
      >
        <FormModel.Item label="模板名称" prop="name">
          <Input
            placeholder="请输入模板名称"
            v-model={this.dataSource.name}
            disabled={!this.isEdit || !hasPermission(this.permission)}
            maxLength={15}
          />
        </FormModel.Item>
        <FormModel.Item label="模板说明" prop="description">
          <div class="relative">
            <Input.TextArea
              placeholder="请输入模板说明"
              size="large"
              v-model={this.dataSource.description}
              disabled={!this.isEdit || !hasPermission(this.permission)}
              maxLength={200}
              style={{ resize: 'none', minHeight: '88px' }}
            />
            <div class="text-14px text-#999 absolute" style={{ right: '4px', bottom: '3px', lineHeight: '1' }}>
              <span style={{ color: this.dataSource.description?.length === 200 ? '#F04040' : 'inherit' }}>
                {this.dataSource.description?.length || 0}
              </span>
              {` / `}
              <span>{200}</span>
            </div>
          </div>
        </FormModel.Item>
      </FormModel>
    );
  },
});

export default TemplateForm;
