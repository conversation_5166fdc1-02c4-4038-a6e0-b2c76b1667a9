<template>
  <div class="person-container">
    <transition name="fade">
      <div class="wrap" v-if="isLoaded">
        <company-logo class="logo" :id="keyNo" :src="image" :hasimage="!!image" :name="name" size="40px"></company-logo>
        <div class="content-container">
          <div>
            <q-entity-link style="font-size: 14px; line-height: 22px" :coy-obj="{ KeyNo: id, Name: name }" />
          </div>
          <div class="row-item" v-if="type !== 'risk' && tags && tags.length">
            <q-tag :type="tag.font" :key="tag.name" v-for="tag in tags">{{ tag.name }}</q-tag>
          </div>
          <div class="row-item to-analysis" v-if="relatedCount">
            <q-link :to="`/pl/${id}/base#relatedcompanylist`">关联{{ relatedCount }}家企业></q-link>
          </div>
          <div class="row-item" v-if="peid && pename">
            <span class="label-text">企业：</span>
            <span class="content">
              <q-entity-link :coy-obj="{ KeyNo: peid, Name: pename }"></q-entity-link>
            </span>
          </div>
          <div class="row-item" v-if="jobTitle">
            <span class="label-text">职位：</span>
            <span class="content">{{ jobTitle || '-' }}</span>
          </div>
          <div class="row-item row-item-btn" v-if="type === 'risk'">
            <q-link :to="`/firm/${id}/tupu#riskChart`"> <div class="btn">查看风险图谱</div></q-link>
          </div>
        </div>
        <div style="clear: both"></div>
        <template v-if="+type === 1 || +type === 3">
          <div class="gap" v-if="keyNo"></div>
          <div class="summary-container">
            <div class="row-item col-item" v-for="(item, index) in summary" :key="index">
              <span class="label-text">{{ item.label }}：</span>
              <span class="content" v-if="item.count">{{ item.count }}家</span>
              <span class="content" v-if="!item.count">-</span>
            </div>
          </div>
        </template>
        <template v-else-if="+type === 2">
          <div class="gap" v-if="keyNo && rsTags"></div>
          <g-app-path v-if="rsTags" :tips="tips" :path-data="pathData" :ename="ename" :eid="eid"></g-app-path>
        </template>
        <template v-if="+type === 3 && pathData && pathData.length">
          <div class="gap" v-if="keyNo"></div>
          <div class="path">
            <div class="path-title-wrap">
              <span class="path-title">关联方认定详情</span>
              <q-tag type="primary" v-if="rule === 'sh'">上交所</q-tag>
              <q-tag type="warning" v-if="rule === 'sz'">深交所</q-tag>
              <q-tag type="pl" v-if="rule === 'kj'">会计准则</q-tag>
            </div>
            <g-app-path-glf :paths="pathData" :key-no="eid" />
          </div>
        </template>
      </div>
    </transition>
    <div v-if="!isLoaded" class="loading"></div>
  </div>
</template>

<script src="./component.js"></script>

<style lang="less" src="./style.less" scoped></style>
