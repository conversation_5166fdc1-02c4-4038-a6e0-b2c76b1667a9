/* eslint-disable no-param-reassign */
import dataLoader from './data';
import appPath from '../g-app-path';
import appPathGlf from '../g-app-path-glf';
import getTypes from '../utils/tag-type';
import CompanyLogo from '@/components/company-logo';

export default {
  name: 'g-app-person',
  data() {
    return {
      isLoaded: false,
      // 人ID
      id: '',
      // 姓名
      name: '',
      // 头像
      image: '',
      // 标签
      tags: [],
      // 公司名
      ename: '',
      // 公司编号
      eid: '',
      // 公司名
      pename: '',
      // 公司编号
      peid: '',
      // 受益人标签
      rsTags: '',
      // 职位
      jobTitle: '',
      summary: [],
      type: 1,
      tips: '',
      relatedCount: 0,
    };
  },
  components: {
    [appPath.name]: appPath,
    [appPathGlf.name]: appPathGlf,
    CompanyLogo,
  },
  mounted() {
    this.keyNo = this.id;
    if (this.keyNo) {
      dataLoader
        .loadPersonDetail(this.eid || '', this.keyNo)
        .then((data) => {
          if (+data.code === 500) {
            this.$toasted.error('查无数据');
          } else if (data.name) {
            this.isLoaded = true;
            this.name = data.name;
            this.image = data.image;
            this.tags = data.tags || [];
            data.tags.forEach((tag) => {
              tag.font = getTypes.getType(tag.type, tag.name);
            });
            this.pename = data.ename;
            this.peid = data.eid;
            this.jobTitle = data.jobTitle;
            this.summary = data.summary;
            this.relatedCount = data.relatedCount;
          }
        })
        .catch(() => {
          // this.$toasted.error(err || '查无数据')
        });
    } else {
      this.isLoaded = true;
    }
  },
};
