import { computed, defineComponent, onMounted, ref } from 'vue';
import { Tooltip } from 'ant-design-vue';
// 常见姓列表
import FAMILY_NAMES from './family-names';

import { formatCompanyLogoUrl } from '@/utils/format/company-format';

import styles from './company-logo.module.less';

import qs from 'querystring';

const AUTO_FLAG = 'image.qcc.com/auto/';

/**
 * 根据ID或名称获取颜色
 * @param id - 企业ID或人员名称
 * @param name - 可选的名称参数，用于人员头像颜色计算
 * @returns 颜色值（不含#前缀）
 */
const getColorById = (id: string, name?: string) => {
  const colors = [
    'E59E9E',
    '72C1A0',
    'E5B072',
    'AE9EE5',
    '9EA6E5',
    '8BABE5',
    '72BCCE',
    'E5A687',
    'C7AE8E',
    '97BB72',
    'D294D2',
    'E5BF72',
    'BD97DF',
    '7BB1DD',
  ];

  let colorIndex = 0;

  if (id) {
    // 企业ID颜色计算
    const index = parseInt(
      String(id)
        .split('')
        .find((s) => s <= 'd') ?? '0',
      16
    );
    colorIndex = index % colors.length;
  } else if (name) {
    // 人员名称颜色计算 - 使用简单的哈希算法
    const hashCode = (str: string) => {
      let hash = 5381;
      for (let i = 0; i < str.length; i++) {
        hash = (hash * 33) ^ str.charCodeAt(i);
      }
      return hash >>> 0; // 转为无符号32位整数
    };
    colorIndex = hashCode(name) % colors.length;
  } else {
    // 随机颜色
    colorIndex = Math.floor(Math.random() * colors.length);
  }

  return colors[colorIndex];
};
// 将以上的颜色映射成对应的颜色
const resetLogoColorMap = {
  E59E9E: 'C7B7B7', // 浅粉 → 灰粉
  E5A687: 'C7B7B7', // 橙粉 → 灰粉
  E5B072: 'C7BEB3', // 橙黄 → 灰米
  C7AE8E: 'C7BEB3', // 卡其 → 灰米
  E5BF72: 'BFC7B7', // 土黄 → 灰绿
  '97BB72': 'BFC7B7', // 草绿 → 灰绿
  '72C1A0': 'B7C7C0', // 蓝绿 → 灰青
  '72BCCE': 'B7C7C0', // 青蓝 → 灰青
  '7BB1DD': 'AFBCC7', // 浅蓝 → 灰蓝
  '8BABE5': 'AFBCC7', // 天蓝 → 灰蓝
  '9EA6E5': 'B8B3C7', // 浅紫蓝 → 灰紫
  AE9EE5: 'B8B3C7', // 紫色 → 灰紫
  BD97DF: 'C7B7C7', // 浅紫 → 灰粉紫
  D294D2: 'C7B7C7', // 紫红 → 灰粉紫
};

export const LogoImg = defineComponent({
  props: {
    id: {
      type: String,
      required: true,
    },
    name: {
      type: String,
      default: '',
    },
    size: {
      type: String,
      default: '32px',
    },
    hasimage: {
      type: Number,
      default: 1,
    },
    src: {
      type: String,
      default: '',
    },
  },
  setup(props) {
    const isError = ref(false);
    //判断一下是否通过URL参数渲染
    const urlRenderParams = computed<null | { shortName: string; logoColor: string }>(() => {
      if (props.src?.includes(AUTO_FLAG)) {
        let params: null | { shortName: string; logoColor: string } = null;
        try {
          const queryObj = qs.parse(props.src.split('?')?.[1]) as { shortName: string; color: string } | undefined;
          if (queryObj?.shortName) {
            params = {
              shortName: queryObj.shortName,
              logoColor: queryObj?.color || '',
            };
          }
          return params;
        } catch (error) {}
      }
      return null;
    });

    /**
     * 计算图片URL
     */
    const url = computed(() => {
      if (props.src && !urlRenderParams.value?.shortName) {
        return props.src;
      }
      return formatCompanyLogoUrl(props.id, props.hasimage);
    });

    /**
     * 计算背景颜色
     */
    const logoColor = computed(() => {
      const logoColor = urlRenderParams.value?.logoColor || getColorById(props.id, props.name);
      return resetLogoColorMap[logoColor] || logoColor;
    });

    /**
     * 获取显示文本
     * 支持复姓处理和企业名称截取
     */
    const getDisplayText = (): string => {
      if (!props.name) return '';
      if (urlRenderParams.value?.shortName) {
        return urlRenderParams.value.shortName;
      }

      // 移除HTML标签
      const cleanName = props.name.replace(/<[^>]+>/g, '');

      if (props.id?.[0] === 'p') {
        // 人员姓名处理
        let displayText = cleanName[0] || '';

        // 检查是否为复姓
        if (cleanName.length >= 3) {
          const surname = cleanName.substr(0, 2);
          if (FAMILY_NAMES.includes(surname)) {
            displayText = surname;
          }
        }

        return displayText;
      } else {
        // 企业名称处理 - 最多显示4个字符
        return cleanName.length > 4 ? cleanName.substring(0, 4) : cleanName;
      }
    };

    /**
     * 计算字体大小
     */
    const getFontSize = (size: number): string => {
      const dszie = size - 2;
      const nameLength = getDisplayText()?.length;
      switch (nameLength) {
        case 1:
          return `${Math.ceil(dszie / 1.8)}px`;
        case 2:
          return `${Math.ceil(dszie / 2.6)}px`;
        case 3:
        case 4:
          return `${Math.ceil(dszie / 3)}px`;
        default:
          return `${Math.ceil(dszie / 3.6)}px`;
      }
    };

    onMounted(() => {
      if (props.hasimage === 0) {
        isError.value = true;
      } else {
        !urlRenderParams.value;
      }
    });

    return {
      isError,
      url,
      logoColor,
      urlRenderParams,
      getFontSize,
      getDisplayText,
    };
  },
  render() {
    // 判断是否显示文本logo
    const shouldShowTextLogo = this.urlRenderParams || this.isError || !this.url;

    if (shouldShowTextLogo) {
      const size = parseInt(this.size, 10);
      const fontSize: string = this.getFontSize(size);
      const scale = parseInt(fontSize, 10) / 12;
      const displayText = this.getDisplayText();

      const style: Record<string, any> = {};

      // 处理字体大小和缩放
      if (parseInt(fontSize, 10) < 12) {
        // 谷歌浏览器12px 限制
        style.fontSize = '12px';
        style.width = `${100 / scale}%`;
        style.height = `${100 / scale}%`;
        style.transform = `scale(${scale})`;
        style.transformOrigin = 'top left';
      } else if (fontSize !== '18px') {
        style.fontSize = fontSize;
      }

      const formattedText = displayText.length > 2 ? displayText.replace(/(.{2})(?=.)/, '<span>$1</span>') : `<span>${displayText}</span>`;

      return (
        <div
          style={{
            width: this.size,
            height: this.size,
            backgroundColor: `#${this.logoColor}`,
          }}
          class="inline-block"
        >
          <span data-testid="logo-text" class={styles.textLogo} style={style} domPropsInnerHTML={formattedText}></span>
        </div>
      );
    }

    // 显示图片logo
    return (
      <img
        src={this.url}
        onError={() => {
          this.isError = true;
        }}
        onLoad={() => {
          this.isError = false;
        }}
      />
    );
  },
});

const CompanyLogo = defineComponent({
  props: {
    id: {
      type: String,
      required: true,
    },
    name: {
      type: String,
      required: false,
    },
    hasimage: {
      type: Number,
      default: 1,
    },
    src: {
      type: String,
      default: '',
    },
    size: {
      type: String,
      default: '32px',
    },
    hoverable: {
      type: Boolean,
      default: false,
    },
  },
  render() {
    return (
      <Tooltip overlayClassName={styles.overlay} placement="rightTop" mouseEnterDelay={this.hoverable ? 0 : 99999}>
        <LogoImg
          slot="title"
          id={this.id}
          name={this.name}
          style={{ width: '100px', height: '100px', borderRadius: '4px' }}
          size={'100px'}
          hasimage={this.hasimage}
          src={this.src}
        />
        <LogoImg
          id={this.id}
          name={this.name}
          style={{ width: this.size, height: this.size, borderRadius: '4px', border: '1px solid #eee' }}
          hasimage={this.hasimage}
          src={this.src}
          size={this.size}
        />
      </Tooltip>
    );
  },
});

export default CompanyLogo;
