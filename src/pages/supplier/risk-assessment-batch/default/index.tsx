import qs from 'querystring';
import { defineComponent, ref, computed, provide, nextTick } from 'vue';
import { Spin, message } from 'ant-design-vue';
import { useRoute, useRouter } from 'vue-router/composables';
import { cloneDeep } from 'lodash';

import { batchImport } from '@/shared/services';
import HeroicLayout from '@/shared/layouts/heroic';
import BatchUpload from '@/shared/components/batch-upload';
import { useAbility } from '@/libs/plugins/user-ability';
import { useMultiSettingStore } from '@/hooks/use-multi-setting-store';
import { objectValuesToNumeric } from '@/utils/transform/object/object-values-to-numeric';
import { useRoomSocket } from '@/hooks/use-room-socket/use-room-socket';
import { BatchBusinessTypeEnum } from '@/shared/constants/batch-business-type.constant';
import { createTrackEvent, useTrack } from '@/config/tracking-events';
import CommonResult from '@/shared/components/common-result';
import { useDataHook } from '@/shared/components/common-result/hooks/use-data-hook';
import { useI18n } from '@/shared/composables/use-i18n';

import GuideWidget from '../widgets/guide';
import styles from './risk-assessment-batch.module.less';
import env from '@/shared/config/env';
import DiligenceSettingSwitch from '@/shared/features/switch-setting-model';

const RiskAssessmentBatch = defineComponent({
  name: 'RiskAssessmentBatch',
  setup() {
    const init = ref(true);
    const resRef = ref();
    const route = useRoute();
    const router = useRouter();
    const pageKey = route?.name || route?.params?.pageType;
    const { dataSource } = useDataHook(pageKey);
    const track = useTrack();
    const getParams = computed(() => {
      return {
        batchType: 0,
        businessType: [BatchBusinessTypeEnum.Diligence_File, BatchBusinessTypeEnum.Diligence_ID, BatchBusinessTypeEnum.Diligence_Customer],
        pageSize: 10,
        pageIndex: 1,
      };
    });
    const hasData = computed(() => {
      return dataSource.value.length > 0;
    });

    // 更新单个任务状态
    const updateBatchItem = (incomingItem: Record<string, any>) => {
      const oriData = cloneDeep(dataSource.value);
      const changeIndex = oriData.findIndex((item) => +incomingItem.batchId === item.batchId);
      if (changeIndex > -1) {
        oriData[changeIndex] = {
          ...oriData[changeIndex],
          status: +incomingItem.status,
          canRetry: +incomingItem.canRetry,
          statisticsInfo: {
            ...oriData[changeIndex].statisticsInfo,
            ...objectValuesToNumeric(incomingItem.statisticsInfo),
          },
        };
        dataSource.value = oriData;
      }
    };

    const ability = useAbility();

    const checkUsage = (
      features: string[] = ['DiligenceCompanyQuantity', 'DiligenceHistoryQuantity', 'DiligenceDailyQuantity']
    ): Promise<boolean> => {
      return ability.check('stock', features);
    };

    provide('abilityCheck', checkUsage);

    provide('needMessage', false);

    const { selectedModel } = useMultiSettingStore();

    const fetchData = async () => {
      await nextTick();
      resRef.value?.search?.();
    };

    useRoomSocket(env.WEBSOCKET_BASE_URL, {
      filter: (messageData) =>
        messageData.roomType === 'BatchProcessMonitor' &&
        [BatchBusinessTypeEnum.Diligence_File, BatchBusinessTypeEnum.Diligence_ID, BatchBusinessTypeEnum.Diligence_Customer].includes(
          +messageData.data.businessType
        ),
      update: updateBatchItem,
      refresh: fetchData,
    });

    const updateData = async (data) => {
      if (
        data?.toDetail &&
        data?.batchId
        // (await ability.check('stock', [
        //   'DiligenceCompanyQuantity',
        //   'DiligenceHistoryQuantity',
        //   'DiligenceDailyQuantity',
        // ]))
      ) {
        return router.push({
          name: 'upload-confirm',
          params: {
            type: 'batch-investigation',
          },
          query: {
            batchId: data.batchId,
          },
        });
      }
      // 选择上传后
      message.success('批量排查任务正在进行中！请稍后在批量排查任务中查看结果');
      return fetchData();
    };

    const gotoDetail = (record) => {
      track(createTrackEvent(6208, '批量排查', '排查详情'));
      router.push({
        path: `/supplier/batch-investigation/detail/${record.batchId}`,
        query: {
          settingId: record?.batchInfo?.settingId,
        },
      });
    };

    const { tc } = useI18n();

    return {
      hasData,
      ability,
      init,
      getParams,
      resRef,
      dataSource,
      selectedModel,
      checkUsage,
      updateData,
      fetchData,
      gotoDetail,
      tc,
    };
  },
  render() {
    return (
      <HeroicLayout align={this.hasData ? undefined : 'center'}>
        <div slot="hero">
          <DiligenceSettingSwitch style={{ position: 'absolute', right: '15px', top: '15px' }} />
          <header class={styles.hero}>
            <div class={styles.title}>{this.tc('Batch Risk Checks')}</div>
            <div class={styles.batch}>
              <BatchUpload
                action={(file) =>
                  `/rover/batch/import/diligence/excel?${qs.stringify({
                    fileName: file.name,
                    settingId: this.selectedModel?.id,
                  })}`
                }
                // beforeFileUpload={() => this.checkUsage()}
                // 多种新建任务的最后都会去出触发这个事件
                onUpdateData={this.updateData}
              />
            </div>
          </header>
        </div>
        {/* 新手引导 */}
        <Spin spinning={this.init}>
          <GuideWidget v-show={!this.hasData} />

          {/* 因为需要调用search，用v-show */}
          <CommonResult
            ref="resRef"
            v-show={this.hasData}
            title={`${this.tc('Batch Risk Checks')}任务`}
            rowKey={'batchId'}
            searchFn={batchImport?.search}
            scroll={{ x: true }}
            filterParams={this.getParams}
            onUpdateCache={() => {
              this.init = false;
            }}
            onAction={this.gotoDetail}
            onExport={() => this.$track(createTrackEvent(6208, '批量排查', '导出名单'))}
            onRetry={this.fetchData}
          ></CommonResult>
        </Spin>
      </HeroicLayout>
    );
  },
});

export default RiskAssessmentBatch;
