import { computed, defineComponent } from 'vue';
import { useRouter } from 'vue-router/composables';

import HeroicLayout from '@/shared/layouts/heroic';
import QCard from '@/components/global/q-card';
import RiskTrendsTab from '@/components/tabs';
import { createTrackEvent } from '@/config/tracking-events';
import { BatchBusinessTypeEnum } from '@/shared/constants/batch-business-type.constant';

import SearchBlock from '../widgets/search-block';
import HistoryBlockBatch from '../../../../shared/components/history-block-batch';
import { BATCH_GUIDE_DATA, TABS } from '../config';
import { BiddingSettingSwitch } from '@/shared/features/switch-setting-model';
import { useStore } from '@/store';

const BiddingBatch = defineComponent({
  name: 'BiddingBatch',
  setup() {
    const router = useRouter();
    const store = useStore();
    const settingId = computed(() => store.getters['biddingMultiSettings/selectedModel'].id);
    const goBiddingSingle = () => {
      router.push({
        name: 'bidding-investigation',
      });
    };
    return {
      goBiddingSingle,
      settingId,
    };
  },
  render() {
    return (
      <HeroicLayout
        bodyStyle={{
          background: '#fff',
        }}
      >
        <QCard
          slot="hero"
          bodyStyle={{
            paddingTop: 0,
          }}
        >
          <RiskTrendsTab slot="title" tabs={TABS} value={'bidding-batchTask'} onChange={this.goBiddingSingle} />

          <BiddingSettingSwitch slot="extra" />

          <SearchBlock
            isBatch={true}
            settingId={this.settingId}
            onSearch={() => {
              (this.$refs.histortyBlock as any).fetchData();
            }}
          >
            <template slot="title">批量招标排查</template>
          </SearchBlock>
        </QCard>

        {/* 有历史数据 */}
        <HistoryBlockBatch
          ref="histortyBlock"
          businessType={BatchBusinessTypeEnum.Bidding_Diligence_File}
          guideData={BATCH_GUIDE_DATA}
          onAction={(record) => {
            this.$track(createTrackEvent(6951, '批量招标排查', '排查详情'));
            this.$router.push({
              name: 'bidding-batch-detail',
              params: {
                type: 'bidding-batch',
              },
              query: {
                batchId: record.batchId,
                useCacheQuery: 'false',
              },
            });
          }}
        />
      </HeroicLayout>
    );
  },
});

export default BiddingBatch;
