import qs from 'querystring';
import { computed, defineComponent, onMounted, ref } from 'vue';
import { message } from 'ant-design-vue';
import { get, uniqBy } from 'lodash';
import { useRoute } from 'vue-router/composables';

import QIcon from '@/components/global/q-icon';
import BatchUpload<PERSON>rame from '@/shared/components/batch-upload-frame';
import QCard from '@/components/global/q-card';
import { hasPermission } from '@/shared/composables/use-permission';
import { createTrackEvent, useTrack } from '@/config/tracking-events';
import FileUpload from '@/shared/components/file-upload';
import DownloadLink from '@/shared/components/download-link';
import { useBiddingSettingStore } from '@/hooks/use-bidding-setting-store';
import { InvestigationType } from '@/shared/config/interest-investigation-setting.config';
import { investigationConfig } from '@/pages/supplier/bidding-investigation/widgets/search-block/constants';
import { openBatchAddDialog, openBatchPasteDialog } from '@/shared/composables/open-dialog-drawer';
import { useInterestSetting } from '@/hooks/use-interest-setting';

import styles from './styles.module.less';

const SearchBlock = defineComponent({
  name: 'SearchBlock',
  props: {
    isBatch: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: InvestigationType.bidding,
    },
    settingId: {
      type: Number,
      required: false,
    },
  },
  emits: ['change', 'search'],
  setup(props, { emit }) {
    const track = useTrack();
    const route = useRoute();
    const interestCompanyUploadMaxCount = ref(0);
    const { getCompanyUploadMaxCount } = useBiddingSettingStore();
    const { getCompanyLimit } = useInterestSetting();
    const config = computed(() => investigationConfig[props.type]);

    onMounted(async () => {
      if (props.type === InvestigationType.bidding) {
        interestCompanyUploadMaxCount.value = await getCompanyUploadMaxCount();
      } else {
        interestCompanyUploadMaxCount.value = await getCompanyLimit();
      }
    });

    const handleBatchAddCompany = async (options = {}) => {
      const openDialog = get(options, 'tabs.0.key') === 'paste' ? openBatchPasteDialog : openBatchAddDialog;
      const selectedItems = await openDialog({
        ...options,
        validate: (data) => {
          if (data?.length < 2) {
            message.warn('排查企业不能少于2家');
            return false;
          }
          if (data?.length > interestCompanyUploadMaxCount.value) {
            message.warn(`单次排查企业上限为${interestCompanyUploadMaxCount.value}`);
            return false;
          }
          return true;
        },
      });
      emit('change', uniqBy(selectedItems, 'companyId'));
    };

    const DEFAULT_PLACEHOLDER = '点击或拖拽文件到此上传';
    const isChecking = ref(false);
    const placeholder = ref(DEFAULT_PLACEHOLDER);

    const resetPlaceholder = () => {
      isChecking.value = false;
      placeholder.value = DEFAULT_PLACEHOLDER;
    };

    const setTimer = () => {
      isChecking.value = true;
      const timer = setTimeout(() => {
        placeholder.value = '文件正在努力解析中，请稍后…';
      }, 3000);
      return timer;
    };

    const beforeFileUpload = async (file: File) => {
      const timer = setTimer();
      try {
        const { errorMsg } = await config.value.uploadCheck({ file });
        if (errorMsg) {
          message.warning(errorMsg);
          resetPlaceholder();
          return false;
        }
        return true;
      } catch (error) {
        resetPlaceholder();
        return false;
      } finally {
        clearTimeout(timer);
      }
    };

    const handleTrack = (btnName) => {
      const code = props.isBatch ? config.value.trackCode.batch : config.value.trackCode.single;
      track(createTrackEvent(code, route.meta?.title, btnName));
    };

    return {
      config,
      interestCompanyUploadMaxCount,
      handleBatchAddCompany,
      beforeFileUpload,
      isChecking,
      placeholder,
      resetPlaceholder,
      handleTrack,
    };
  },
  render() {
    const renderContent = () => {
      // 批量排查（上传文件）
      if (this.isBatch) {
        return (
          <div class="flex" style={{ margin: '0 auto', maxWidth: '910px' }}>
            <BatchUploadFrame title="上传文档" style="flex: 1;">
              <FileUpload
                action={(file) =>
                  `${this.config.uploadUrl}?${qs.stringify({
                    fileName: file.name,
                    settingId: this.settingId
                  })}`
                }
                height="134px"
                placeholder={this.placeholder}
                showUploadList={false}
                beforeFileUpload={this.beforeFileUpload}
                loading={this.isChecking}
                onSuccess={() => {
                  this.resetPlaceholder();
                  this.$emit('search');
                  this.handleTrack('上传文档成功');
                }}
                onReject={this.resetPlaceholder}
                theme="lighter"
              />
              <ul slot="description">
                <li>
                  <DownloadLink href={this.config.templateUrl} download="上传文件模板.xlsx" onClick={() => this.handleTrack('下载模板')}>
                    下载模板
                  </DownloadLink>
                  <span> 并按照样式编辑好数据，切勿增减列</span>
                </li>
                <li>上传文件不超过2M，仅支持Excel</li>
                <li>
                  {this.config.name}一次支持最多不超过&nbsp;<em>1000</em>&nbsp; 个项目，单个项目排查企业数不超过&nbsp;
                  <em>{this.interestCompanyUploadMaxCount}</em>&nbsp; 家
                </li>
              </ul>
            </BatchUploadFrame>
          </div>
        );
      }

      return (
        <div class="flex" style={{ margin: '0 auto', gap: '20px', justifyContent: 'center' }}>
          <BatchUploadFrame title="输入文本" width="600px">
            <div
              class={[styles.uploadContainer]}
              onClick={() => {
                // if (!hasPermission(this.config.permission.batch)) {
                //   message.warn('您暂无批量添加企业权限，请联系管理员');
                //   return;
                // }

                this.handleTrack('输入文本');

                this.handleBatchAddCompany({
                  tabs: [{ key: 'paste', label: '输入文本' }],
                });
              }}
            >
              <div class={styles.icon}>
                <QIcon type="icon-wenbenniantie" />
              </div>
              <div class={styles.placeholder}>
                <p class={styles.text}>
                  <div class="faker-blink"></div>
                  {` 点击输入或粘贴企业名录`}
                </p>
              </div>
            </div>
            <ul slot="description">
              <li>
                单次支持输入不超过&nbsp;<em>{this.interestCompanyUploadMaxCount}</em>&nbsp;家企业
              </li>
              <li>可输入企业名称、统一社会信用代码等关键词</li>
              <li>支持自动模糊匹配对应企业</li>
            </ul>
          </BatchUploadFrame>
          <BatchUploadFrame title="选择企业" width="290px">
            <div class={styles.buttonContainer}>
              <div
                class={styles.button}
                onClick={() => {
                  if (!hasPermission(this.config.permission.third)) {
                    message.warn('您暂无从第三方列表添加企业权限，请联系管理员');
                    return;
                  }
                  this.handleTrack('选择企业');
                  this.handleBatchAddCompany({
                    tabs: [{ key: 'third', label: '从第三方列表选择' }],
                  });
                }}
              >
                <QIcon type="icon-tianjia" style={{ fontSize: '12px', padding: '1px' }} />
                <span>从第三方列表中选择</span>
              </div>
            </div>
            <ul slot="description">
              <li>
                单次排查企业数量不超过&nbsp;<em>{this.interestCompanyUploadMaxCount}</em>&nbsp;家
              </li>
            </ul>
          </BatchUploadFrame>
        </div>
      );
    };

    return (
      <QCard bodyStyle={{ padding: '30px' }}>
        <h2 class="text-center" style={{ fontSize: '32px', lineHeight: '1.5', marginBottom: '20px' }}>
          {this.$slots.title}
        </h2>
        {renderContent()}
      </QCard>
    );
  },
});

export default SearchBlock;
