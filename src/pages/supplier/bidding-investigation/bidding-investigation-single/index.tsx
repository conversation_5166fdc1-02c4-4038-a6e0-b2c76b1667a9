import { defineComponent, nextTick, ref } from 'vue';
import { useRouter } from 'vue-router/composables';
import { debounce } from 'lodash';

import HeroicLayout from '@/shared/layouts/heroic';
import QCard from '@/components/global/q-card';
import RiskTrendsTab from '@/components/tabs';
import { createTrackEvent, useTrack } from '@/config/tracking-events';
import RiskAction from '@/shared/components/risk-action';
import QIcon from '@/components/global/q-icon';
import HistoryBlockSingle from '@/shared/components/history-block-single';
import { useBiddingStorage } from '@/hooks/use-bidding-storage';
import GuideBlock from '@/shared/components/guide-block';
import { useRoomSocket } from '@/hooks/use-room-socket/use-room-socket';

import { SINGLE_GUIDE_DATA, TABS } from '../config';
import SearchBlock from '../widgets/search-block';
import env from '@/shared/config/env';
import { BiddingSettingSwitch } from '@/shared/features/switch-setting-model';
import { useStore } from '@/store';

const BiddingInvestigationPage = defineComponent({
  name: 'BiddingInvestigationPage',
  setup() {
    const store = useStore();
    const router = useRouter();
    const track = useTrack();
    const { updateSelectedCompanyList, resetProjectInfo } = useBiddingStorage();
    /**
     * 执行搜索
     */
    const handleBiddingSearch = async (selectedItems: any[]) => {
      updateSelectedCompanyList(selectedItems); // 保存选中项
      resetProjectInfo(); // 重置项目信息
      await nextTick(); // 由于 sessionStorage 操作有 IO 延迟，保证数据存储成功后再跳转

      router.push({
        name: 'bidding-investigation-verify',
        query: {
          settingId: store.getters['biddingMultiSettings/selectedModel'].id,
        },
      });
    };

    /**
     * 标签切换
     */
    const handleTabChange = () => {
      router.push({
        name: 'bidding-batch',
      });
      track(createTrackEvent(6224, '招标排查', '批量招标排查'));
    };

    /**
     * 招标排查历史-点击事件
     */
    const handleItemClick = (itemId: string) => {
      track(createTrackEvent(6224, '招标排查', '招标排查历史'));
      router.push({
        name: 'bidding-investigation-detail',
        params: {
          type: 'bidding-investigation',
        },
        query: {
          id: itemId,
        },
      });
    };

    const historyBlockRef = ref(null);
    const socketUpdater = debounce(async (data) => {
      if (data.status !== '0') {
        await nextTick();
        historyBlockRef.value?.getSearchHistory();
      }
    }, 300);
    useRoomSocket(env.WEBSOCKET_BASE_URL, {
      eventType: 'TenderDiligenceChanged',
      filter: (messageData) => messageData.roomType === 'TenderDiligence',
      refresh: socketUpdater,
      update: socketUpdater,
    });

    return {
      handleBiddingSearch,
      handleTabChange,
      handleItemClick,
      historyBlockRef,
    };
  },
  render() {
    return (
      <HeroicLayout
        bodyStyle={{
          background: '#fff',
        }}
      >
        <QCard
          slot="hero"
          bodyStyle={{
            paddingTop: 0,
          }}
        >
          <RiskTrendsTab slot="title" tabs={TABS} value={'bidding-investigation'} onChange={this.handleTabChange} />

          <BiddingSettingSwitch slot="extra" />

          {/* 搜索入口 */}
          <SearchBlock isBatch={false} onChange={this.handleBiddingSearch}>
            <template slot="title">招标风险排查</template>
          </SearchBlock>
        </QCard>

        {/* 有历史数据 */}
        <HistoryBlockSingle ref={'historyBlockRef'} onItemClick={this.handleItemClick}>
          <RiskAction
            slot="action"
            onClick={() => {
              this.$track(createTrackEvent(6224, '招标排查', '更多历史记录'));
              this.$router.push({
                name: 'bidding-investigation-history',
              });
            }}
          >
            {'更多历史记录'}
            <QIcon type="icon-wenzilianjiantou"></QIcon>
          </RiskAction>
          <template slot="guide">
            <GuideBlock guideData={SINGLE_GUIDE_DATA} />
          </template>
        </HistoryBlockSingle>
      </HeroicLayout>
    );
  },
});

export default BiddingInvestigationPage;
