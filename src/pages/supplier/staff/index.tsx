import { defineComponent, ref, onMounted, computed, unref, watch } from 'vue';
import { Button, message, Checkbox, Space } from 'ant-design-vue';
import { omit, isEmpty, isEqual } from 'lodash';

import CommonSearchFilter from '@/components/common/common-search-filter';
import { openAddStaffModal } from '@/components/modal/supplier/add-staff-modal';
import HeroicLayout from '@/shared/layouts/heroic';
import { staff as staffService } from '@/shared/services';
import QCard from '@/components/global/q-card';
import { useMenuStore } from '@/hooks/use-menu-store';
import { useAbility } from '@/libs/plugins/user-ability';
import { openStaffDetailDrawer } from '@/components/modal/supplier/staff-detail-drawer';
import UserCountStatistics from '@/components/user-count-statistics';
import { useUsageStore } from '@/hooks/use-usage-store';
import { createTrackEvent, useTrack } from '@/config/tracking-events';
import { getFilters } from '@/hooks/use-options';
import { syncUsage } from '@/hooks/use-sync-usage-hook';
import DropdownButtonWrapper from '@/components/dropdown-button-wrapper';
import { openGroupEditModal } from '@/components/modal/supplier/add-partner-modal/hooks';
import { useCommonSettingStore } from '@/hooks/use-common-settings';
import CommonResult from '@/shared/components/common-result';
import { useTrackEvent } from '@/hooks/use-track-event';
import { desensitization } from '@/utils/string';
import { uuid } from '@/utils';

import { ORIGINAL_TABLE_COLUMN, STAFF_SEARCH_FILTER_GROUPS } from './config';
import RelativeTable from './widgets/relative-table';
import SearchCount from '@/components/search-count';
import { useRequest } from '@/shared/composables/use-request';

const StaffPage = defineComponent({
  name: 'StaffPage',
  setup() {
    const resRef = ref();
    const init = ref(true);
    const uniqId = ref(uuid());
    const {
      selectRows,
      dataSource,
      selectedIds,
      pagination,
      sortInfo,
      btnDisable,
      showBatchData,
      showBatchSelectAll,
      generateSelectData,
      isFixed,
    } = useCommonSettingStore({ idKey: 'id', key: 'staff', columnData: ORIGINAL_TABLE_COLUMN });

    const track = useTrack();
    const { handleSearchTrack } = useTrackEvent('人员管理');
    const showModal = ref(false);
    const filterGroups = ref<any[]>(STAFF_SEARCH_FILTER_GROUPS.slice());
    getFilters(unref(filterGroups), 'Person', '添加人', '').then((res) => {
      filterGroups.value = res;
    });
    const groups = ref([]);
    const filters = ref();
    const isShowStaffDetail = ref(false);

    const selectAll = ref(false);
    const personNos: any = ref([]);

    const isRelativeClick = ref(false); // 是否是亲属详情查看

    const getParams = computed(() => {
      const payload = filters.value;
      return {
        searchKey: payload?.keywords,
        groupIds: payload?.filters?.g || undefined,
        operators: payload?.filters?.t,
        updateDate: payload?.filters?.sd ? [payload.filters?.sd] : undefined,
        personNos: personNos.value.length > 0 ? personNos.value : undefined,
        pageIndex: pagination.current,
        pageSize: pagination.pageSize,
      };
    });

    const autoExpandedRowKeys = ref([]);
    const searchText = computed(() => getParams.value.searchKey || getParams.value.personNos?.join('|'));
    const isFilterChange = ref(false);

    const searchFn = async (params) => {
      const res = await staffService.getList(params);
      if (isFilterChange.value) {
        autoExpandedRowKeys.value = res.data?.filter((v) => !!v.relatives?.length).map((v) => v.id) || [];
      }
      return {
        ...res,
        realTotal: res.total,
        total: res.counts?.employeeCount,
      };
    };

    const { execute, isLoading, data } = useRequest(searchFn);

    watch(
      () => omit(getParams.value, ['pageIndex', 'pageSize', 'groupIds']),
      (n, o) => {
        if (isEqual(n, o)) {
          return;
        }
        if (Object.values(n).some((v) => !isEmpty(v))) {
          isFilterChange.value = true;
        } else {
          isFilterChange.value = false;
          autoExpandedRowKeys.value = [];
        }
      }
    );

    const countsInfo = computed(() => ({ ...data.value?.counts, total: data.value?.realTotal }));

    const getGroups = async () => {
      const res = await staffService.aggsSearch({
        query: {},
        aggsField: 'group',
      });
      groups.value = res.map((group: any) => ({
        name: group.fieldLabel,
        groupId: +group.fieldValue,
        count: group.count,
        value: +group.fieldValue,
        label: group.fieldLabel,
      }));
      filterGroups.value[0].options = groups.value;
    };

    const getAllIds = async (params?) => {
      const res = await staffService.getList(
        params || {
          selectAll: true,
          personNos: personNos.value,
        }
      );
      selectRows.value = res.personIds.map((id) => {
        return {
          id,
        };
      });
    };

    const handlerDelete = async (params) => {
      const { ids, key } = params;
      if (!ids?.length && key === 'batch') {
        message.warning('请选择需要移除的人员');
        return;
      }
      await staffService.delStaff(
        key === 'all' ? { ...omit(getParams.value, ['pageSize', 'pageIndex', 'current']), selectAll: true } : { ids }
      );
      message.success('移除成功');
      // 当批量选中且全部删除后，返回默认列表
      if (showBatchData.value && selectAll.value) {
        showBatchData.value = false;
        personNos.value = [];
      }
      pagination.current = 1;
      selectRows.value = [];
      selectAll.value = false;
      syncUsage('personQuantity', -ids.length);
      getGroups();
      resRef?.value?.search();
    };

    const updateGroup = async (groupId) => {
      const params = {
        groupId,
        personIds: selectedIds.value,
      };
      await staffService.batchStaff(params);
      message.success('移动分组成功');
      getGroups();
      resRef?.value?.search();
    };

    const openGroupModal = async () => {
      track(createTrackEvent(6238, '人员管理', '移动分组'));
      await openGroupEditModal({
        groups: unref(groups),
        groupType: 2,
        updateGroup,
      });
    };

    const handleFilterChange = (payload) => {
      pagination.current = 1;
      filters.value = payload;
    };

    // 导出人员列表
    const handleExport = async () => {
      try {
        await staffService.batchExport(getParams.value);
        message.success('正在导出，稍后可前往任务列表查看进度');
      } catch (error) {
        console.error(error);
      }
    };

    const handleExportByIds = async () => {
      try {
        if (!selectedIds.value.length) {
          message.warning('请选择需要导出的人员');
          return;
        }
        await staffService.batchExport({ ids: selectedIds.value });
        message.success('正在导出，稍后可前往任务列表查看进度');
      } catch (error) {
        console.error(error);
      }
    };

    const isFiltersEmpty = computed(() => {
      return isEmpty(filters.value?.filters) && !filters.value?.keywords;
    });

    const handleOpenStaffDetailDrawer = async (staffData) => {
      const { countInfo } = staffData;
      const res = await openStaffDetailDrawer({
        countInfo,
        groups: groups.value,
      });
      if (res) {
        resRef.value?.search();
      }
    };

    const { hasUsage } = useUsageStore();

    onMounted(async () => {
      init.value = true;
      getGroups();
    });

    // 是否是初次进入页面
    const isFirstImport = computed(() => {
      return pagination?.current === 1 && isEmpty(filters.value) && pagination.total === 0 && !init.value;
    });

    // 更新cache的数据
    const update = ({ type, value }) => {
      const { total, data, pageIndex, pageSize } = value;
      switch (type) {
        case 'init':
          init.value = false;
          dataSource.value = data;
          pagination.current = pageIndex;
          pagination.total = total;
          pagination.pageSize = pageSize;
          break;
        case 'pagination':
          pagination.current = pageIndex;
          pagination.pageSize = pageSize;
          break;
        default:
          Object.assign(sortInfo, value);
      }
    };

    const searchRef = ref<HTMLElement>();
    const ability = useAbility();
    // 修改或者增加人员
    const generateStaffList = async ({ type = '', id = '', relatives = null } = {}) => {
      // 如果是新增，先判断用量
      if (!id) {
        if (!(await ability.check('stock', ['PersonQuantity']))) {
          return;
        }
      }
      openAddStaffModal({
        staffId: id,
        current: type,
        groups: groups.value,
        groupId: filters.value?.filters?.g?.length === 1 ? filters.value?.filters?.g[0] : -1,
        ok: (newRelatives) => {
          if (id) {
            message.success('修改成功');
          } else {
            message.success('添加成功');

            (searchRef.value as any)?.reset?.();
            pagination.current = 1;
          }
          // 如果亲属信息从 有 变到 没有，则更新uniqId
          if (!isEqual(relatives, newRelatives) && !newRelatives) {
            uniqId.value = uuid();
          }
          syncUsage('personQuantity', 1);
          resRef?.value?.handlePageChange(pagination.current, pagination.pageSize);
          getGroups();
        },
      });
    };

    const { currentTitle } = useMenuStore();

    return {
      init,
      uniqId,
      resRef,
      btnDisable,
      filterGroups,
      dataSource,
      pagination,
      showModal,
      handlerDelete,
      updateGroup,
      getGroups,
      selectedIds,
      groups,
      isFiltersEmpty,
      handleFilterChange,
      handleExport,
      handleExportByIds,
      filters,
      isShowStaffDetail,
      isRelativeClick,
      handleOpenStaffDetailDrawer,
      hasUsage,
      sortInfo,
      selectAll,
      showBatchData,
      getAllIds,
      selectRows,
      personNos,
      openGroupModal,
      generateSelectData,
      showBatchSelectAll,
      getParams,
      isFirstImport,
      update,
      generateStaffList,
      currentTitle,
      handleSearchTrack,
      execute,
      isLoading,
      countsInfo,
      autoExpandedRowKeys,
      searchText,
    };
  },
  render() {
    const openStaffDrawer = (data) => {
      this.handleOpenStaffDetailDrawer({
        countInfo: data.countInfo,
      });
      const label = ORIGINAL_TABLE_COLUMN.find((v) => v.scopedSlots?.customRender === data.countInfo.tabType)?.title;
      if (label) {
        this.$track(createTrackEvent(6238, '人员管理', label));
      }
    };
    const staffSlots = {
      staffBirthInfo: (item) => {
        const birthDay = item.birthDay || '-';
        let area = item.province ? item.province : '-';
        area = item.city ? `${area}/${item.city}` : area;
        area = item.district ? `${area}/${item.district}` : area;
        const showData = [birthDay, area].filter((v) => v !== '-');
        if (showData.length === 0) {
          return '-';
        }
        return (
          <div>
            {showData.map((pv) => (
              <div>{pv}</div>
            ))}
          </div>
        );
      },
      staffGroups: (value) => {
        if (!value) {
          return '未分组';
        }
        const group: any = this.groups.filter((item: any) => item.value === value)[0];
        return group?.label || '未分组';
      },
      staffHireInfo: (scope) => {
        const nameMap = {
          djgCount: '持股/任职企业',
          holdingCount: '控制企业',
          phoneCount: '联系方式关联企业',
        };

        const renderCountButton = (key) => {
          const currentCount = scope.companyCountInfo?.[key] || '-';
          if (currentCount === '-') {
            return '-';
          }
          return (
            <q-button
              type="link"
              onClick={() => {
                openStaffDrawer({
                  countInfo: {
                    ...scope.companyCountInfo,
                    tabType: key,
                    name: scope.name,
                    keyNo: scope.keyNo,
                  },
                });
              }}
            >
              {currentCount}
            </q-button>
          );
        };
        const staffHireList = Object.keys(nameMap);
        return (
          <div>
            {staffHireList.map((skey) => (
              <div class="flex items-center" key={skey}>
                <span>{nameMap[skey]}：</span>
                <span>{renderCountButton(skey)}</span>
              </div>
            ))}
          </div>
        );
      },
      staffConnect: (record) => {
        const connectList = [record.phone || '', record.email || ''].filter(Boolean);
        if (connectList.length === 0) {
          return '-';
        }
        return connectList.map((data) => {
          return (data || '').split(',').map((phNo) => (
            <div class="overflow-hidden text-ellipsis whitespace-nowrap w-120px" title={desensitization(phNo)}>
              {desensitization(phNo)}
            </div>
          ));
        });
      },
    };
    return (
      <HeroicLayout loading={this.init}>
        <QCard
          slot="hero"
          title={this.currentTitle}
          bodyStyle={{
            paddingTop: 0,
          }}
        >
          <CommonSearchFilter
            ref={'searchRef'}
            placeholder="姓名、人员编号"
            batchPlaceholder="请输入人员和编号，不同的人员或编号之间请通过换行间隔。\n输入样例：\n张三\n1234"
            filterConfig={this.filterGroups}
            // inputWidth={'265px'}
            multiSearch={true}
            defaultValue={this.filters}
            onUpdateBatchData={async (ids: []) => {
              this.personNos = ids;
              this.pagination.current = 1;
              this.showBatchData = true;
            }}
            onChange={(filterData, group) => {
              this.selectRows = [];
              this.handleFilterChange(filterData);
              this.handleSearchTrack(6236, { keyword: filterData.keywords, filter: group.label });
            }}
            onMultiClear={() => {
              this.showBatchData = false;
              this.selectAll = false;
              this.selectRows = [];
              this.personNos = [];
            }}
            onReset={() => this.$track(createTrackEvent(6238, '人员管理', '重置筛选'))}
          />
        </QCard>
        {/* 搜索结果 */}
        <CommonResult
          key={this.uniqId}
          ref="resRef"
          rowKey={'id'}
          useCache={true}
          needSelect={true}
          searchFn={this.execute}
          selectedIds={this.selectedIds}
          showIndex={false}
          isFixed={false}
          expandedDesc={'展开亲属信息'}
          defaultExpandedRowKeys={this.autoExpandedRowKeys}
          filterParams={this.getParams}
          groups={this.groups}
          scroll={{ x: false, y: 'calc(100vh - 146px - 260px)' }}
          columns={ORIGINAL_TABLE_COLUMN}
          onUpdateCache={this.update}
          onSelect={(values) => {
            this.generateSelectData(values);
            this.selectAll = this.showBatchData && this.selectRows.length === this.pagination.total;
          }}
          onDelete={(ids) => {
            this.handlerDelete({ ids, key: 'batch' });
          }}
          onEdit={(value) => {
            this.generateStaffList({ id: value.id, relatives: value.relatives });
          }}
          onSingleAddData={() => {
            this.generateStaffList({});
            this.$track(createTrackEvent(6238, '人员管理', '新增人员'));
          }}
          onShowDetail={(data) => openStaffDrawer(data)}
          searchKey={this.searchText}
          showExpanded={(staffInfo) => staffInfo.relatives?.length > 0}
          extraSlots={{
            ...staffSlots,
            expandedRowRender: (scope) => {
              return <RelativeTable key={scope.personNo} searchKey={this.searchText} record={scope} slots={staffSlots} />;
            },
          }}
        >
          {this.showBatchSelectAll && (
            <div style="margin-bottom: 15px;" slot="batchSelect">
              <Checkbox
                style={{ height: '22px' }}
                defaultChecked={this.selectAll}
                indeterminate={this.selectedIds.length > 0 && this.selectedIds.length < this.pagination.total}
                checked={this.selectAll}
                onChange={(e) => {
                  this.selectAll = e.target.checked;
                  if (e.target.checked) {
                    this.getAllIds();
                  } else {
                    this.selectRows = [];
                  }
                }}
              >
                全部选中
              </Checkbox>
            </div>
          )}
          <div slot="title">
            <div slot="title" class="flex items-center">
              <SearchCount
                showSelects
                total={this.countsInfo.employeeCount}
                loading={this.isLoading}
                selectedIds={this.selectedIds}
                scopedSlots={{
                  message: (loading) => {
                    return <div class="flex items-center">共找到{loading}名员工</div>;
                  },
                }}
              />
              <SearchCount
                showSelects
                total={this.countsInfo.relativeCount}
                loading={this.isLoading}
                scopedSlots={{
                  message: (loading) => {
                    return <div class="flex items-center">，{loading}名近亲属</div>;
                  },
                }}
              />
            </div>
          </div>
          <div slot="extra">
            <div class="space-x-10px">
              <UserCountStatistics dimension={'personQuantity'} />
              <DropdownButtonWrapper
                v-permission={[2065]}
                totalCount={this.pagination?.total}
                btnText="移除人员"
                selectIdlength={this.selectedIds.length}
                confirmText={'此操作不可恢复，您确认移除该人员吗？'}
                onConfirm={(key) => {
                  this.handlerDelete({ key, ids: this.selectedIds });
                  this.$track(createTrackEvent(6238, '人员管理', '移除人员'));
                }}
              />
              <Button
                v-permission={[2064]}
                v-disable-tip={'请先选择要操作的人员'}
                v-show={this.dataSource}
                disabled={this.btnDisable}
                v-debounceclick={this.openGroupModal}
              >
                移动分组
              </Button>

              <DropdownButtonWrapper
                totalCount={this.pagination?.total}
                v-permission={[2066]}
                btnText="导出列表"
                selectIdlength={this.selectedIds.length}
                needPopConfirm={false}
                menuItems={[
                  {
                    label: '选中人员',
                    key: 'exportByIds',
                  },
                  {
                    label: '全部人员',
                    key: 'export',
                  },
                ]}
                onConfirm={(key) => {
                  if (key === 'export') {
                    this.handleExport();
                  } else {
                    this.handleExportByIds();
                  }
                  this.$track(createTrackEvent(6238, '人员管理', '导出列表'));
                }}
              />
              <Button
                v-permission={[2062]}
                type={'primary'}
                icon="plus-circle"
                onClick={async () => {
                  this.generateStaffList();
                  this.$track(createTrackEvent(6238, '人员管理', '新增人员'));
                }}
              >
                新增人员
              </Button>
            </div>
          </div>
          <div slot="emptyExtra">
            <Space v-permission={[2062]} v-show={this.isFirstImport} style="margin-top: 15px;">
              <Button
                onClick={() => {
                  this.generateStaffList({});
                }}
              >
                单个添加
              </Button>
              <Button
                onClick={() => {
                  this.generateStaffList({ type: 'bulk' });
                  this.$track(createTrackEvent(6238, '人员管理', '批量导入'));
                }}
                type="primary"
              >
                批量导入
              </Button>
            </Space>
          </div>
        </CommonResult>
      </HeroicLayout>
    );
  },
});

export default StaffPage;
