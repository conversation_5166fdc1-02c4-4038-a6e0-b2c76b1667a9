import { defineComponent, nextTick, onMounted, ref, watch } from 'vue';
import { Breadcrumb, Spin, message } from 'ant-design-vue';
import { useRoute, useRouter } from 'vue-router/composables';
import { uniqBy } from 'lodash';

import QIcon from '@/components/global/q-icon';
import { biddings as biddingsService } from '@/shared/services';
import { useFetchState } from '@/hooks/use-fetch-state';
import { useBiddingSettingStore } from '@/hooks/use-bidding-setting-store';
import { createTrackEvent, useTrack } from '@/config/tracking-events';
import { statusToPageMap } from '@/shared/config/bidding-investigation-detail.config';
import { useBiddingStorage } from '@/hooks/use-bidding-storage';

import { useDiligenceInBidding } from './hooks/use-diligence-in-bidding';
import MatchResult from './widgets/match-result';
import ProjectInfoForm from './widgets/project-info-form';

const BiddingInvestigationVerify = defineComponent({
  setup() {
    const track = useTrack();
    const router = useRouter();
    const route = useRoute();
    const isBidding = ref(false);

    const BiddingCompanyUploadMaxCount = ref(0);
    const { getCompanyUploadMaxCount, isOpenQualification } = useBiddingSettingStore();

    // Storage
    const { selectedCompanyList, updateSelectedCompanyList, projectInfo, updateProjectInfo } = useBiddingStorage();
    // 从 Storage 中恢复已选数据
    const currentSelectedCompanyList = ref(selectedCompanyList.value);
    // 保存与 Storage 同步
    watch(
      currentSelectedCompanyList,
      (selectedItems) => {
        updateSelectedCompanyList(selectedItems);
      },
      {
        immediate: true,
      }
    );

    const dataSource = ref<Record<string, any>[]>([]);

    const { fetchCompaniesRiskLevel, updateDataSource } = useDiligenceInBidding();

    /**
     * 获取待排查的企业列表
     */
    const fetchCompanyList = async () => {
      const ids = currentSelectedCompanyList.value.map((item) => item.companyId);
      if (ids.length === 0) {
        return [];
      }
      const res = await biddingsService.getCompanyList({
        filter: { ids },
        includeFields: ['creditcode', 'id', 'reccap', 'reccapmount', 't_type', 'scope'],
        pageSize: BiddingCompanyUploadMaxCount.value,
      });
      const result = res.Result || [];
      dataSource.value = await fetchCompaniesRiskLevel(result); // 写入 dataSource
      return result;
    };

    const { execute, isLoading, isIdle } = useFetchState(fetchCompanyList);

    onMounted(async () => {
      BiddingCompanyUploadMaxCount.value = await getCompanyUploadMaxCount();
      execute();
    });

    // 开始排查
    const startBidding = async (formData) => {
      isBidding.value = true;
      const { projectName, projectNo, keys, necessaryFlag } = formData;
      const payload = {
        keyNoAndNames: currentSelectedCompanyList.value,
        projectName,
        projectNo,
        certification: {
          keys,
          necessaryFlag,
        },
        settingId: route.query.settingId ? Number(route.query.settingId) : undefined,
      };
      const res = await biddingsService.getRelations(payload);

      const name = statusToPageMap[res.status];
      if (!name) return;
      await router.push({
        name,
        params: {
          type: 'bidding-investigation',
        },
        query: {
          id: String(res.id),
        },
      });
      isBidding.value = false;
    };

    const handleSubmit = async (formData) => {
      if (dataSource.value?.length < 2) {
        message.warn('排查企业不能少于2家');
        return;
      }
      // 保存项目信息
      updateProjectInfo({
        ...formData,
      });
      await nextTick(); // 由于 sessionStorage 操作有 IO 延迟，保证数据存储成功后再跳转
      await startBidding(formData);
      track(createTrackEvent(8119, '招标排查核实', '开始排查'));
    };

    /**
     * 从列表中移除公司
     */
    const handleRemoveCompany = (item) => {
      currentSelectedCompanyList.value = currentSelectedCompanyList.value.filter((i) => i.companyId !== item.id);
      dataSource.value = dataSource.value.filter((v) => v.id !== item.id);
      track(createTrackEvent(8119, '招标排查核实', '移除企业'));
    };

    /**
     * 添加企业
     */
    const handleAddCompany = (companies: any[]) => {
      // 添加到待排查列表中
      currentSelectedCompanyList.value = uniqBy(currentSelectedCompanyList.value.concat(companies), 'companyId');
      execute(); // 重新搜索
      track(createTrackEvent(8119, '招标排查核实', '添加企业'));
    };

    const handleOpenRiskDetail = (riskInfo) => {
      const route = router.resolve({
        name: 'supplier-investigate-detail',
        params: {
          type: 'investigation-history',
          id: riskInfo.companyId,
        },
        query: {
          diligenceId: riskInfo.id,
          from: 'record',
        },
      });
      window.open(route.href, '_blank');
    };

    return {
      projectInfo,
      handleSubmit,
      dataSource,
      isOpenQualification,
      isLoading,
      isIdle,
      isBidding,

      currentSelectedCompanyList,

      handleAddCompany,
      handleRemoveCompany,
      BiddingCompanyUploadMaxCount,
      handleOpenRiskDetail,
      updateDataSource,
    };
  },
  render() {
    return (
      <div style={{ paddingTop: '40px' }}>
        <div class="breadcrumb">
          <Breadcrumb>
            <Breadcrumb.Item>
              <a
                onClick={() =>
                  this.$router.replace({
                    name: 'bidding-investigation',
                  })
                }
              >
                <QIcon type="icon-mianbaoxiefanhui" />
                招标排查
              </a>
            </Breadcrumb.Item>
            <Breadcrumb.Item>资质筛查</Breadcrumb.Item>
          </Breadcrumb>
        </div>
        {this.isBidding ? (
          <div
            style={{
              backgroundColor: '#fff',
              minHeight: 'calc(100vh - 112px)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              borderRadius: '4px',
            }}
          >
            <Spin spinning />
          </div>
        ) : (
          <MatchResult
            minHeight="calc(100vh - 162px)"
            max={this.BiddingCompanyUploadMaxCount}
            selected={this.currentSelectedCompanyList}
            dataSource={this.dataSource}
            onAdd={this.handleAddCompany}
            onRemove={this.handleRemoveCompany}
            showDiligence={true}
            onOpenRiskDetail={this.handleOpenRiskDetail}
            onUpdateData={(record) => this.updateDataSource(this.dataSource, record)}
            isLoading={this.isLoading}
            isIdle={this.isIdle}
          >
            <ProjectInfoForm dataSource={this.projectInfo} onSubmit={this.handleSubmit} loading={this.isLoading || this.isIdle} />
          </MatchResult>
        )}
      </div>
    );
  },
});

export default BiddingInvestigationVerify;
