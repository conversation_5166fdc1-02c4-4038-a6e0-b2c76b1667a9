import { defineComponent, onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router/composables';
import { message as Message } from 'ant-design-vue';

import HeroicLayout from '@/shared/layouts/heroic';
import CompanySearch from '@/shared/components/company-search';
import { createTrackEvent, useTrack } from '@/config/tracking-events';
import { useI18n } from '@/shared/composables/use-i18n';
import { isValidCompanyType } from '@/utils/company/company-type';
import { routerTryPush } from '@/shared/composables/use-auth';

import { COMPANY_EXAMPLES } from './config/example.config';
import Guide from './widgets/guide';
import RandomLinks from './widgets/random-links';
import styles from './investigation.page.module.less';
import DiligenceSettingSwitch from '@/shared/features/switch-setting-model';

/**
 * 风险排查-准入排查
 */
const InvestigationPage = defineComponent({
  name: 'Investigation',
  setup() {
    const track = useTrack();
    onMounted(() => {
      track(createTrackEvent(6202, '风险排查-准入排查'));
    });

    const router = useRouter();
    const route = useRoute();
    const defaultKeywords = ref(route.query.name);
    const defaultKeyNo = ref(route.query.keyNo);
    const handleSearch = () => {
      Message.error('必须选择一个企业后开始排查');
    };

    const handleSelect = (option) => {
      if (option?.IsHide) {
        Message.warning('涉嫌非法社会组织不支持排查');
      } else if (!isValidCompanyType(option?.type)) {
        Message.warning('注册地在中国香港、中国澳门、中国台湾以及境外的企业、机关单位等暂不支持风险排查');
      } else {
        router
          .replace({
            name: 'supplier-investigate-detail',
            params: {
              type: 'investigation',
              id: option.id,
            },
            query: {
              diligenceId: undefined,
              from: 'investigation',
            },
          })
          .catch(console.error);
      }
    };

    const { tc } = useI18n();

    return {
      defaultKeywords,
      defaultKeyNo,
      handleSearch,
      handleSelect,
      tc,
    };
  },
  render() {
    const { tc } = this;
    return (
      <HeroicLayout align="center">
        <div slot="hero" class={styles.hero}>
          <CompanySearch
            title={tc('Third-party Risk Screening')}
            placeholder={tc('Please enter a company name/USCI')}
            enterText={tc('Search')}
            onSearch={this.handleSearch}
            onSelect={this.handleSelect}
            defaultKeywords={this.defaultKeywords as string}
            defaultKeyNo={this.defaultKeyNo as string}
            onGoToBatch={() => {
              routerTryPush(this.$router, { name: 'batch-investigation' });
            }}
          />
          <DiligenceSettingSwitch style={{ position: 'absolute', right: '15px', top: '15px' }} />
        </div>
        <div class={styles.example}>
          <Guide>
            <RandomLinks dataSource={COMPANY_EXAMPLES} />
          </Guide>
        </div>
      </HeroicLayout>
    );
  },
});

export default InvestigationPage;
