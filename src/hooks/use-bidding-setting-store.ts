import { get } from 'lodash';
import { computed, unref } from 'vue';
import store from '@/store';

export const useBiddingSettingStore = () => {
  const getSetting = async (id?: number) => {
    await store.dispatch('biddings/getBiddingSetting', id);
  };

  const createSetting = async () => {
    await store.dispatch('biddings/createBiddingSetting');
  };

  const recoverySetting = async (id) => {
    await store.dispatch('biddings/recoveryBiddingSetting', id);
  };

  const result = computed(() => store.state.biddings.biddingSettings);

  const getCompanyUploadMaxCount = async () => {
    if (!result.value?.content?.length) {
      await getSetting();
    }
    const CompanyUploadMaxCount = unref(result)?.content?.find((setting) => setting.key === 'BiddingCompanyCountSetting');
    return get(CompanyUploadMaxCount, `subDimensionList.0.value`) || 20;
  };

  // 是否打开资质审查的按钮
  const isOpenQualification = computed(() => {
    if (!result.value?.content?.length) {
      return false;
    }
    return !!unref(result)?.content?.find((setting) => setting.key === 'BiddingCompanyCertification')?.status;
  });

  return {
    result,
    isOpenQualification,
    getCompanyUploadMaxCount,
    getSetting,
    createSetting,
    recoverySetting,
  };
};
