import { intersection, isArray } from 'lodash';

import { getRiskLevelStyle } from '@/config/risk.config';

import { regMatch } from '../../pages/supplier/risk-trends/hooks/content-helper';

export const TenderResultList = [
  {
    label: '通过',
    value: 0,
    type: 'success',
    style: {
      color: '#00AD65',
      backgroundColor: '#F7FCFA',
      borderColor: '#94DCBE',
    },
  },
  {
    label: '审慎核实',
    value: 1,
    type: 'warning',
    style: {
      color: '#FFAA00',
      backgroundColor: '#FFFBF7',
      borderColor: '#FFCE94',
    },
  },
  {
    label: '不通过',
    value: 2,
    type: 'danger',
    style: {
      color: '#F04040',
      backgroundColor: '#FFFAFA',
      borderColor: '#FFBCBC',
    },
  },
  {
    label: '稍后核定',
    value: -1,
    type: 'shade',
    style: {
      color: '#999',
      backgroundColor: '#F9F9F9',
      borderColor: '#d8d8d8',
    },
  },
];

const strongLevelDimension = [
  'biddingcompanyrelationship', // 直接关系
  'BiddingCompanyRelationship', // 直接关系
  'legal',
  'employ',
  'invest',
  'hislegal',
  'hisemploy',
  'hisinvest',
  'branch',
  'actualcontroller',
  'guarantor',
  'equitypledge',
  'chattelmortgage',
  'controlrelation',
  '法定代表人',
  '董监高',
  '股东',
  '历史股东',
  '持股/投资关联',
  '历史法定代表人',
  '历史董监高',
  '历史持股/投资关联',
  '分支机构',
  '控制关系',
  '实际控制人',
  '相互担保关联',
  '股权出质关联',
  '动产抵押关联',
];
const normalLevelDimension = [
  'contactnumber',
  'mail',
  'address',
  'upanddownrelation',
  'bidcollusive',
  'website',
  '相同电话号码',
  '相同邮箱',
  '相同经营地址',
  '上下游关联',
  '围串标关联',
  '客户',
  '供应商',
  '相同域名信息',
  'contactrelationship',
];
const weakLevelDimension = [
  'patent',
  'intpatent',
  'softwarecopyright',
  'case',
  'samenameemployee',
  '相同专利信息',
  '相同国际专利信息',
  '相同软件著作权',
  '相同司法案件',
  '疑似同名主要人员',
];
/**
 * 获取关系类型 风险强弱等级 样式, 图谱的关系可能出现多种
 * @param dimension: [] | string
 * @returns
 */
export const getTenderRelationLevelStyle = (dimension: any[] | string = []) => {
  let level = 3;
  let label = '';
  const dimensionData = isArray(dimension) ? dimension.filter((item) => item).map((d) => d.toLowerCase()) : [dimension.toLowerCase()];
  if (intersection(strongLevelDimension, dimensionData).length) {
    level = 2;
    label = '强关系';
  } else if (intersection(normalLevelDimension, dimensionData).length) {
    level = 1;
    label = '中等关系';
  } else if (intersection(weakLevelDimension, dimensionData).length) {
    level = 0;
    label = '弱关系';
  } else {
    level = 3;
    label = '';
  }
  const style = getRiskLevelStyle(level);
  return {
    style: {
      ...style,
      width: '62px',
      'text-align': 'center',
      transform: 'translateY(-2px)',
      color: '#333',
    },
    label,
  };
};

export const SuggestionsList = TenderResultList.filter((item) => item.value !== 1);

export const TenderResultMap = TenderResultList.reduce((r, t) => {
  r[`${t.value}`] = t;
  return r;
}, {});

export const TenderSuggestionMap = SuggestionsList.reduce((r, t) => {
  r[`${t.value}`] = t;
  return r;
}, {});

export const DIMENSION_DESCRIPTION_MAP = {
  BiddingCompanyRelation: {
    tips: '企查查通过排查多家投标供应商之间的交叉持股、董监高相互任职、同集团企业等关系， 规避围标、串标、陪标等风险。重点关注投标人之间存在关联关系、不同投标人与高级管理人员之间存在交叉任职、人员混用或者亲属关系、等围标串标高风险迹象。',
    suggest:
      '建议对存在投资任职关联的企业进行<b>重点核查</b>，进一步判断该关联关系是否会影响本次招标。对存在潜在关联/利益关联方的企业进行<b>进一步核实</b>，必要时由投标供应商出具澄清函予以澄清说明。 ',
  },

  JointBiddingAnalysis: {
    tips: '企查查对供应商历史共同投标信息进行分析，对潜在经常性“抱团”投标进行分析，重点关注中标率异常低、不以中标为目的投标的“陪标专业户”以及中标率异常高的“标王”。',
    suggest:
      '建议对存在中标率异常的企业进行<b>重点核查</b>，结合企业资质情况，进一步分析中标率异常原因，判断该风险是否会影响本次招标，必要时由投标供应商出具澄清函予以澄清说明。',
  },
  risk_inner_blacklist: {
    tips: '企查查通过排查投标方与黑名单企业的关系，避免内部黑名单企业利用关联企业投标。',
    suggest: '建议对黑名单关联企业进行<b>重点核查</b>，排除不合格的投标人或重新选择投标企业。',
  },
  risk_interest_conflict: {
    tips: '企查查通过排查供应商与企业内部员工的潜在关系，避免商业贿赂、利益输送等风险。',
    suggest: '建议对存在疑似同名、相同联系方式员工与投标企业关系进行<b>核实</b>，必要时由投标供应商出具澄清函予以澄清说明。',
  },
  PurchaseIllegal: {
    tips: '企查查通过排查供应商历史涉采购不良行为记录，规避不良供应商投标。',
    suggest: '建议对涉采购不良行为记录企业进行<b>重点核查</b>，排除不合格的投标人或重新选择投标企业。',
  },
  BiddingCompanyCertification: {
    tips: '企查查通过排查候选供应商是否具备投标所需要的资质情况，以供排查人全面掌握候选人的资质情况。',
    suggest: '建议对无有效资质信息的企业进行<b>重点核查</b>，进一步判断无有效资质信息是否会影响本次招标。',
  },
};

/**
 * 将描述内容中包含的公司名称替换为带链接的公司链接
 */
export const getLinkDesc = (data: { description?: string; keyNoAndNames?: any[] }) => {
  let text = data.description;
  if (!text) {
    text = '-';
  }
  const highlights =
    data.keyNoAndNames?.map((item) => {
      return {
        ...item,
        Name: item.companyName,
        Id: item.companyId,
      };
    }) || [];
  text = regMatch(
    highlights,
    text,
    highlights,
    (v) => `<a href="/embed/companyDetail?keyNo=${v.id}&title=${v.name}" target="_blank">${v.name}</a>`
  );
  return text;
};

export const statusToPageMap = {
  0: 'bidding-investigation-loading',
  1: 'bidding-investigation-detail',
  2: 'bidding-investigation-failed',
};

export const statusToExternalPageMap = {
  0: 'external-bidding-investigation-loading',
  1: 'external-bidding-investigation-detail',
  2: 'external-bidding-investigation-failed',
};
