import qs from 'querystring';
import { cloneDeep } from 'lodash';

import type { HttpClient } from '@/utils/http-client';

export const SETTING_BASE = '/settings';
export const SETTING_INFO = `${SETTING_BASE}`;
export const UPDATE_BASIC_SETTING = `${SETTING_BASE}/basic`;
export const UPDATE_RISK_SETTING = `${SETTING_BASE}/risk`;
export const UPDATE_PARTNER_SETTING = `${SETTING_BASE}/partner`;
export const UPDATE_INNERBLACKLIST_SETTING = `${SETTING_BASE}/innerBlacklist`;
export const UPDATE_OUTERBLACKLIST_SETTING = `${SETTING_BASE}/outerBlacklist`;
export const UPDATE_CONFLICT_SETTING = `${SETTING_BASE}/conflict`;
export const UPDATE_QUALIFICATION_SETTING = `${SETTING_BASE}/qualification`;
export const RISKFILTER_SORT = `${SETTING_BASE}/move`;
export const DILIGENCE_SETTING = `${SETTING_BASE}/configuration`;
export const MONITOR_NOTIFY = `${SETTING_BASE}/user/configuration/monitor_notify`;
export const TENDER_RISK = `${SETTING_BASE}/configuration/tender_risk`;
export const SETTING_COMPARE = `/settings/compare`;
export const MONITOR_SETTING = `${SETTING_BASE}/monitor`;
export const MONITOR_SETTING_REST = `${SETTING_BASE}/monitor/restore`;
export const GENERATERSAKEY = '/user_configuration/monitor_notify2/generatesecretKey';
export const INTERFACEPUSHDEMO = '/monitor/interface_push_demo';
export const RECOVERYDILIGENCESETTING = `${SETTING_BASE}/risk/restoreById`;
export const DILIGENCE_DEFAULT_SETTING = `/user_configuration/diligence_default_setting`;
export const TENDER_DEFAULT_SETTING = `/user_configuration/tender_default_setting`;
export const TENDER_MULTI_ADD = `/settings/tender/multi/add`;
export const TENDER_MULTI_LIST = `/settings/tender/multi/list`;
export const TENDER_MULTI_UPDATE = `/settings/tender/multi/update`;
export const TENDER_MULTI_DELETE = `/settings/tender/multi/delete`;
export const TENDER_MULTI_SORT = `/settings/tender/multi/sort`;
export const TENDER_SETTING_RECOVERY = `/settings/tender/restoreById`;

function transformSettingResponse(response: Record<string, any>) {
  const { content = {} } = response || {};
  const { version: v, ...settings } = content;
  return settings;
}

const transformSetting = (result: Record<string, any>) => {
  const data = cloneDeep(result).content;
  return data;
};

export const createService = (httpClient: HttpClient) => ({
  async info(params = {}): Promise<any> {
    const res = await httpClient.get(SETTING_INFO, { params });
    return res.content;
  },
  async history(params = {}): Promise<any> {
    const res = await httpClient.get(SETTING_INFO, { params });
    const content = transformSettingResponse(res);
    return {
      ...res,
      content: { ...res.content, ...content },
    };
  },
  // template groupVersion v1普通 v2蔡司
  async template(groupVersion: string, type = 'diligence_risk') {
    return httpClient.get(`/settings/system?${qs.stringify({ type, groupVersion })}`);
  },
  // 多模板 列表
  async list(data = {}): Promise<any> {
    const res = await httpClient.post('/settings/multi/list', data);
    return res;
  },
  // 多模板 新增
  async create(data): Promise<any> {
    const res = await httpClient.post('/settings/multi/add', {
      ...data,
      content: {
        ...transformSetting(data),
        version: data?.content.version || data.groupVersion,
      },
      id: undefined,
      systemSettingsId: data.systemSettingsId || data.id,
    });
    return res;
  },
  // 多模板 编辑
  async edit(data): Promise<any> {
    return httpClient.post('/settings/multi/update', {
      ...data,
      content: {
        ...transformSetting(data),
        version: data?.content.version || data.groupVersion,
      },
    });
  },
  async remove(id): Promise<any> {
    return httpClient.post(`/settings/multi/delete?id=${id}`);
  },
  async setDefaultModel(version): Promise<any> {
    return httpClient.post(DILIGENCE_DEFAULT_SETTING, {
      settingVersion: version,
    });
  },
  async getDefaultModel(): Promise<any> {
    return httpClient.get(DILIGENCE_DEFAULT_SETTING);
  },
  async getUpgradeInfo(params = { type: 'diligence_risk' } as any): Promise<any> {
    const res = await httpClient.post(`${SETTING_COMPARE}?${qs.stringify(params)}`);
    if (params.type === 'diligence_risk') {
      res.content = transformSettingResponse(res);
    }
    return res;
  },

  async update(data): Promise<any> {
    return httpClient.post(SETTING_INFO, {
      ...data,
      content: {
        ...transformSetting(data),
        version: data?.content.version || data.groupVersion,
      },
    });
  },
  async sort(data): Promise<any> {
    return httpClient.post(`/settings/multi/sort`, data);
  },
  updateBasicSetting(data): Promise<any> {
    return httpClient.post(UPDATE_BASIC_SETTING, data);
  },
  updateRiskSetting(data): Promise<any> {
    return httpClient.post(UPDATE_RISK_SETTING, data);
  },
  updatePartnerSetting(data): Promise<any> {
    return httpClient.post(UPDATE_PARTNER_SETTING, data);
  },
  updateInnerBlackListSetting(data): Promise<any> {
    return httpClient.post(UPDATE_INNERBLACKLIST_SETTING, data);
  },
  updateOuterBlackListSetting(data): Promise<any> {
    return httpClient.post(UPDATE_OUTERBLACKLIST_SETTING, data);
  },
  updateConflictSetting(data): Promise<any> {
    return httpClient.post(UPDATE_CONFLICT_SETTING, data);
  },
  updateQualificationSetting(data): Promise<any> {
    return httpClient.post(UPDATE_QUALIFICATION_SETTING, data);
  },
  sortRiskFilterList(data): Promise<any> {
    return httpClient.post(RISKFILTER_SORT, data);
  },
  getDiligenceSetting(): Promise<any> {
    return httpClient.get(DILIGENCE_SETTING);
  },
  updateDiligenceSetting(data): Promise<any> {
    return httpClient.post(DILIGENCE_SETTING, data);
  },
  getMoniTorSetting(type = MONITOR_NOTIFY): Promise<any> {
    return httpClient.get(`/user_configuration/${type}`);
  },
  updateMoniTorSetting(data): Promise<any> {
    return httpClient.post('/user_configuration/monitor_notify2', data);
  },
  async getTenderSetting(params = {}): Promise<any> {
    const res = await httpClient.get('/settings?type=tender_risk', { params });
    return res.content;
  },
  getTenderSettingHistory(params = {}, type = 'tender_risk'): Promise<any> {
    return httpClient.get(`/settings?type=${type}`, { params });
  },
  saveTenderSetting(data, type = 'tender_risk'): Promise<any> {
    return httpClient.post(`/settings?type=${type}`, data);
  },
  getMonitorSetting(id?): Promise<any> {
    const idStr = id ? `?id=${id}` : '';
    return httpClient.get(`${MONITOR_SETTING}${idStr}`);
  },
  saveMonitorSetting(data): Promise<any> {
    return httpClient.post(`${MONITOR_SETTING}`, data);
  },
  // 恢复出厂设置
  resetMonitorSetting(): Promise<any> {
    return httpClient.post(`${MONITOR_SETTING_REST}`);
  },

  /** 舆情监控设置 */
  getMonitorNewsSetting(): Promise<any> {
    return httpClient.get(`/settings/monitor_news`);
  },

  /** 更新舆情监控设置 */
  updateMonitorNewsSetting(data): Promise<any> {
    return httpClient.post(`/settings/monitor_news`, data);
  },

  /** 接口推送设置生成密钥 */
  generateRSAKey(): Promise<any> {
    return httpClient.get(`${GENERATERSAKEY}`);
  },

  /** 接口推送模拟 */
  interfacePushTest(data): Promise<any> {
    return httpClient.post(`${INTERFACEPUSHDEMO}`, data);
  },

  /** 恢复初始设置 */
  recoverydiligenceSetting(id): Promise<any> {
    return httpClient.post(`${RECOVERYDILIGENCESETTING}?id=${id}`);
  },
  tenderMultiAdd(data): Promise<any> {
    return httpClient.post(TENDER_MULTI_ADD, data);
  },
  tenderMultiList(): Promise<any> {
    return httpClient.post(TENDER_MULTI_LIST);
  },
  tenderMultiUpdate(data): Promise<any> {
    return httpClient.post(TENDER_MULTI_UPDATE, data);
  },
  tenderMultiDelete(id): Promise<any> {
    return httpClient.post(`${TENDER_MULTI_DELETE}?id=${id}`);
  },
  tenderMultiSort(data): Promise<any> {
    return httpClient.post(TENDER_MULTI_SORT, data);
  },
  tenderRecovery: (id) => {
    return httpClient.post(`${TENDER_SETTING_RECOVERY}?id=${id}`);
  },
  setTenderDefaultSetting: (data) => {
    return httpClient.post(TENDER_DEFAULT_SETTING, data);
  },
  getTenderDefaultSetting: () => {
    return httpClient.get(TENDER_DEFAULT_SETTING);
  },
});
