import { Tooltip } from 'ant-design-vue';
import { defineComponent } from 'vue';
import styles from './switch-setting-model.module.less';

const SwitchSettingModel = defineComponent({
  name: 'SwitchSettingModel',
  props: {
    title: {
      type: String,
      required: true,
    },
    isMultiple: {
      type: Boolean,
      default: false,
    },
    isLoading: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['switch'],
  render() {
    if (!this.isMultiple || this.isLoading) return null;

    return (
      <Tooltip title="点击切换模型" placement="bottom">
        <div class={[styles.button, 'switch_model']} onClick={() => this.$emit('switch')}>
          <div class={styles.prefix}>
            <q-icon type="icon-switch" />
            <span>模型</span>
          </div>
          <div class={styles.context}>{this.title}</div>
        </div>
      </Tooltip>
    );
  },
});

export default SwitchSettingModel;
