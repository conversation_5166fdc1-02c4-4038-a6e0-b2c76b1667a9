import { defineComponent, nextTick, onMounted, ref, unref } from 'vue';
import { Radio, Spin } from 'ant-design-vue';
import { cloneDeep, isEqual, orderBy } from 'lodash';

import { createPromiseDialog } from '@/components/promise-dialogs';
import QModal from '@/components/global/q-modal';
import { createTrackEvent, useTrack } from '@/config/tracking-events';

import styles from './switch-setting-model-modal.module.less';

type Model = {
  id: number;
  name: string;
  desc: string;
  default?: boolean;
  [x: string]: any;
};

const SwitchModelModal = defineComponent({
  props: {
    params: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props, { emit }) {
    const selected = ref<Model>();
    const visible = ref(true);
    const handleSetDefault = async (item: Model) => {
      props.params.setDefault?.(item);
    };
    const handleCheck = (item: Model) => {
      selected.value = item;
    };

    const handleSubmit = async () => {
      try {
        emit('resolve', selected.value);
        visible.value = false;
      } catch (err) {
        console.error(err);
      }
    };

    const handleCancel = async () => {
      visible.value = false;
      await nextTick();
      emit('resolve', false);
    };

    const init = async () => {
      selected.value = unref(props.params.selectedModel);
    };
    onMounted(async () => {
      init();
    });

    return {
      visible,
      selected,
      handleSetDefault,
      handleCheck,
      handleSubmit,
      handleCancel,
    };
  },
  render() {
    const { list, defaultModel } = this.params;
    return (
      <QModal title="切换模型" v-model={this.visible} onOk={this.handleSubmit} onCancel={this.handleCancel}>
        <Spin spinning={false}>
          <div class={styles.list}>
            {orderBy(list, ['default'], 'desc').map((item) => {
              const isSelected = isEqual(item.id, this.selected?.id);
              const isDefault = item.id === defaultModel.id;
              return (
                <div
                  class={[styles.item, isSelected && styles.itemChecked, 'setting-model-item']}
                  onClick={(e) => {
                    e.preventDefault();
                    this.handleCheck(item);
                  }}
                >
                  <div class="flex justify-between">
                    <span class={styles.title}>
                      <span>{item.name || this.params.defaultTitle}</span>
                      <div class={styles.tag} v-show={isDefault}>
                        默认
                      </div>
                    </span>
                    {!isDefault ? (
                      <div
                        class="choose-default"
                        onClick={(e) => {
                          e.stopPropagation();
                          e.preventDefault();
                          this.handleSetDefault(item);
                          this.$track(createTrackEvent(6981, this.$route?.meta?.title, '设为默认'));
                        }}
                      >
                        <Radio class={styles.radio} v-show={!isDefault}>
                          设为默认
                        </Radio>
                      </div>
                    ) : null}
                  </div>
                  <span v-show={item.description} class={styles.desc}>
                    说明：{item.description}
                  </span>
                  {isSelected ? <q-icon class={styles.checked} type="icon-checked" /> : null}
                </div>
              );
            })}
          </div>
        </Spin>
      </QModal>
    );
  },
});

export default SwitchModelModal;

export const openSwitchModelModal = createPromiseDialog(SwitchModelModal);
