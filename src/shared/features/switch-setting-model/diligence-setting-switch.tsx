import { computed, defineComponent } from 'vue';
import { useRoute } from 'vue-router/composables';

import { useMultiSettingStore } from '@/hooks/use-multi-setting-store';
import { useUserStore } from '@/shared/composables/use-user-store';
import { createTrackEvent, useTrack } from '@/config/tracking-events';
import { setting as settingService } from '@/shared/services';
import SwitchButton from './widgets/switch-button';

import { openSwitchModelModal } from './widgets/switch-setting-model-modal';

const DiligenceSettingSwitch = defineComponent({
  name: 'DiligenceSettingSwitch',
  setup() {
    const track = useTrack();
    const route = useRoute();

    const { execute, list, selectedModel, isLoading, changeSelectModel, defaultModel, setDefault } = useMultiSettingStore();
    execute();

    const handleSwitchModel = async () => {
      track(createTrackEvent(6981, route?.meta?.title, '切换模型'));
      const res = await openSwitchModelModal({
        list,
        defaultModel,
        selectedModel,
        setDefault,
      });
      if (res) {
        track(createTrackEvent(6981, route?.meta?.title, '切换模型确认'));
        await settingService.setDefaultModel(defaultModel.value.version);
        changeSelectModel(res);
      }
    };
    const currentModelName = computed(() => selectedModel.value.name || '第三方风险排查标准模型');
    const { isMultiple } = useUserStore();

    return {
      currentModelName,
      isMultiple,
      handleSwitchModel,
      isLoading,
    };
  },
  render() {
    if (!this.isMultiple || this.isLoading) return null;

    return (
      <SwitchButton
        title={this.currentModelName}
        isMultiple={this.isMultiple}
        isLoading={this.isLoading}
        onSwitch={this.handleSwitchModel}
      ></SwitchButton>
    );
  },
});

export default DiligenceSettingSwitch;
