import { computed, defineComponent, onMounted } from 'vue';
import { useRoute } from 'vue-router/composables';

import { useUserStore } from '@/shared/composables/use-user-store';
import { createTrackEvent, useTrack } from '@/config/tracking-events';
import SwitchButton from './widgets/switch-button';

import { openSwitchModelModal } from './widgets/switch-setting-model-modal';
import { useStore } from '@/store';
import { useRequest } from '@/shared/composables/use-request';
import { setting as settingService } from '@/shared/services';

const BiddingSettingSwitch = defineComponent({
  name: 'BiddingSettingSwitch',
  setup() {
    const track = useTrack();
    const route = useRoute();
    const store = useStore();
    const defaultTitle = '第三方招标排查标准模型';

    const selectedModel = computed(() => store.getters['biddingMultiSettings/selectedModel']);
    const defaultModel = computed(() => store.getters['biddingMultiSettings/defaultModel']);

    const changeSelectModel = (model) => {
      store.commit('biddingMultiSettings/SET_SELECTED_MODEL', model);
    };

    const setDefaultModel = (model) => {
      store.commit('biddingMultiSettings/SET_DEFAULT_MODEL', model);
    };

    const fetchData = async () => {
      const res = await store.dispatch('biddingMultiSettings/getModelsList');
      return res;
    };

    const { execute, isLoading, data } = useRequest(fetchData);

    const handleSwitchModel = async () => {
      track(createTrackEvent(6981, route?.meta?.title, '切换模型'));
      const res = await openSwitchModelModal({
        list: data.value,
        defaultModel,
        selectedModel,
        setDefault: setDefaultModel,
        defaultTitle,
      });
      if (res) {
        track(createTrackEvent(6981, route?.meta?.title, '切换模型确认'));
        settingService.setTenderDefaultSetting({ settingVersion: (res as any).version });
        changeSelectModel(res);
      }
    };
    const currentModelName = computed(() => selectedModel.value.name || defaultTitle);
    const { isMultiple } = useUserStore();

    onMounted(() => {
      execute();
    });

    return {
      currentModelName,
      isMultiple,
      handleSwitchModel,
      isLoading,
    };
  },
  render() {
    if (!this.isMultiple || this.isLoading) return null;

    return (
      <SwitchButton
        title={this.currentModelName}
        isMultiple={this.isMultiple}
        isLoading={this.isLoading}
        onSwitch={this.handleSwitchModel}
      ></SwitchButton>
    );
  },
});

export default BiddingSettingSwitch;
